{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@hookform/resolvers": "^3.9.0", "@mui/icons-material": "^6.4.6", "@mui/material": "^6.1.5", "@mui/x-date-pickers": "^8.3.1", "@react-pdf/renderer": "^4.3.0", "@tanstack/react-query": "^5.59.15", "auth-client": "file:", "axios": "^1.7.7", "client": "file:", "cors": "^2.8.5", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "decimal.js": "^10.5.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "framer-motion": "^12.4.4", "gatsby": "^5.14.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "react-number-format": "^5.4.4", "react-query": "^3.39.3", "react-router-dom": "^6.27.0", "react-select": "^5.10.1", "react-toastify": "^11.0.5", "styled-components": "^6.1.15", "xlsx": "^0.18.5", "yup": "^1.4.0"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.13", "globals": "^15.11.0", "vite": "^5.4.9"}}