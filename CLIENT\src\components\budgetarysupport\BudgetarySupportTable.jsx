import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  TextField,
  IconButton,
  CircularProgress,
  Alert,
  InputAdornment
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import api from '../../config/api';
import { toast } from 'react-toastify';
import { NumericFormat } from 'react-number-format';
import { useUser } from '../../context/UserContext'; // Import auth context

const BudgetarySupportTable = ({ fiscalYear, budgetType }) => {
  const [editMode, setEditMode] = useState(false);
  const [amount, setAmount] = useState(0);
  const queryClient = useQueryClient();
  const { currentUser } = useUser(); // Get current user from auth context

  // Custom component for numeric input formatting
  const NumberFormatCustom = React.forwardRef(function NumberFormatCustom(props, ref) {
    const { onChange, ...other } = props;
    return (
      <NumericFormat
        {...other}
        getInputRef={ref}
        thousandSeparator
        decimalScale={2}
        fixedDecimalScale
        onValueChange={(values) => {
          onChange({
            target: {
              value: values.value,
            },
          });
        }}
      />
    );
  });

  // Check if we have valid fiscalYear and budgetType
  const hasValidParams = !!fiscalYear && !!budgetType;

  // Use React Query for data fetching
  const { 
    data, 
    isLoading, 
    error 
  } = useQuery({
    queryKey: ['budgetary-support', fiscalYear, budgetType],
    queryFn: async () => {
      if (!hasValidParams) {
        console.log('Missing required parameters:', { fiscalYear, budgetType });
        return null;
      }
      
      console.log('Fetching budgetary support with params:', { fiscalYear, budgetType });
      const response = await api.get('/budgetary-support', {
        params: { fiscalYear, budgetType }
      });
      console.log('Budgetary support response:', response.data);
      return response.data && response.data.length > 0 ? response.data[0] : null;
    },
    enabled: hasValidParams, // Only run query if we have valid params
    onError: (err) => {
      console.error('Error fetching budgetary support data:', err);
      toast.error(`Error loading budgetary support data: ${err.message}`);
    }
  });

  // Set amount when data changes
  useEffect(() => {
    if (data) {
      setAmount(data.amount || 0);
    }
  }, [data]);

  const saveMutation = useMutation({
    mutationFn: (newData) => {
      console.log('Saving budgetary support data:', newData);
      if (data && data._id) {
        return api.put(`/budgetary-support/${data._id}`, newData);
      } else {
        return api.post('/budgetary-support', newData);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['budgetary-support', fiscalYear, budgetType]);
      toast.success('Budgetary support data saved successfully');
      setEditMode(false);
    },
    onError: (error) => {
      console.error('Error saving budgetary support:', error);
      toast.error(`Error saving data: ${error.message}`);
    }
  });

  const handleSave = () => {
    if (!hasValidParams) {
      toast.error('Cannot save: Missing fiscal year or budget type');
      return;
    }
    
    const newData = {
      amount: Number(amount),
      fiscalYear,
      budgetType,
      description: 'Operating Requirements',
      // Add these fields to match other components
      processBy: `${currentUser.FirstName} ${currentUser.LastName}`,
      processDate: new Date(),
      region: currentUser.Region,
      status: "Not Submitted"
    };
    
    // Add additional logging
    console.log('Saving budgetary support with data:', newData);
    
    saveMutation.mutate(newData);
  };

  if (!hasValidParams) {
    return (
      <Box sx={{ width: '100%', mb: 4 }}>
        <Alert severity="warning">
          Cannot load budgetary support data: Missing fiscal year or budget type.
        </Alert>
      </Box>
    );
  }

  if (isLoading) {
    return (
      <Box sx={{ width: '100%', mb: 4, display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ width: '100%', mb: 4 }}>
        <Alert severity="error">
          Error loading budgetary support data: {error.message}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', mb: 4 }}>
      <TableContainer component={Paper} sx={{ boxShadow: 3 }}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: 'primary.main' }}>
              <TableCell colSpan={2} sx={{ color: 'white', fontWeight: 'bold' }}>
                BUDGETARY SUPPORT
              </TableCell>
              <TableCell align="right" sx={{ color: 'white', width: '200px' }}>
                {editMode ? (
                  <>
                    <IconButton size="small" onClick={handleSave} sx={{ color: 'white' }}>
                      <SaveIcon />
                    </IconButton>
                    <IconButton size="small" onClick={() => setEditMode(false)} sx={{ color: 'white' }}>
                      <CancelIcon />
                    </IconButton>
                  </>
                ) : (
                  <IconButton size="small" onClick={() => setEditMode(true)} sx={{ color: 'white' }}>
                    <EditIcon />
                  </IconButton>
                )}
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            <TableRow>
              <TableCell colSpan={2} sx={{ pl: 2 }}>
                <Typography variant="subtitle1">Subsidy to the National Irrigation Administration:</Typography>
              </TableCell>
              <TableCell align="right">-</TableCell>
            </TableRow>
            <TableRow>
              <TableCell sx={{ width: '30px' }}>a.</TableCell>
              <TableCell>Operating Requirements</TableCell>
              <TableCell align="right">
                {editMode ? (
                  <TextField
                    size="small"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    sx={{ 
                      width: '180px',
                      '& .MuiInputBase-input': {
                        textAlign: 'right',
                        fontWeight: 'medium',
                        fontSize: '16px'
                      }
                    }}
                    InputProps={{
                      inputComponent: NumberFormatCustom,
                      startAdornment: <InputAdornment position="start">₱</InputAdornment>,
                      style: { textAlign: 'right' }
                    }}
                  />
                ) : (
                  <Typography sx={{ fontWeight: 'medium', fontSize: '16px' }}>
                    ₱ {Number(amount || 0).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </Typography>
                )}
              </TableCell>
            </TableRow>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell colSpan={2} sx={{ fontWeight: 'bold' }}>
                TOTAL BUDGETARY SUPPORT
              </TableCell>
              <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                ₱ {Number(amount || 0).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default BudgetarySupportTable;





