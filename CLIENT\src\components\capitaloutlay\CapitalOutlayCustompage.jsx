import React from "react";
import TextSearchable from "./TextSearchable";

const CustomPage = ({ dataListName, schema }) => {
  const data = []; // Fetch data based on dataListName

  return (
    <div>
      <h1>{dataListName}</h1>
      <table>
        <thead>
          <tr>
            {Object.keys(schema).map((key) => (
              <th key={key}>{schema[key].label}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.length > 0 ? (
            data.map((item, index) => (
              <tr key={index}>
                {Object.keys(schema).map((key) => (
                  <td key={key}>{item[key]}</td>
                ))}
              </tr>
            ))
          ) : (
            <tr>
              {Object.keys(schema).map((key) => (
                <td key={key}>No data</td>
              ))}
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};

export default CustomPage;