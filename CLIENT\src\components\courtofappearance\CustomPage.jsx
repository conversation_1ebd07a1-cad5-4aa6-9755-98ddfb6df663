import React from "react";
import CustomTable from "../courtofappearance/CustomTable";
import DashboardHeader from "../../global/components/DashboardHeader";
import PropTypes from "prop-types";

const CustomPage = ({
  dataListName,
  schema,
  title = "",
  description = "",
  searchable = true,
  customAddElement = null,
  ROWS_PER_PAGE = 20,
  tableData, // override data
}) => {
  const pageTitle =
    title || dataListName.charAt(0).toUpperCase() + dataListName.slice(1);
  const pageDescription = description || `Manage ${dataListName}`;
  const apiPath = `/${dataListName}`;

  return (
    <>
      <DashboardHeader
        title={pageTitle}
        description={pageDescription}
        searchable={searchable}
        childElement={customAddElement} // This places your Save/Cancel buttons
      />

      <CustomTable
        dataListName={dataListName}
        apiPath={apiPath}
        ROWS_PER_PAGE={ROWS_PER_PAGE}
        columns={Object.keys(schema)
          .filter((key) => schema[key].show === true)
          .map((key) => {
            const fieldSchema = schema[key];
            const column = {
              field: key,
              label: fieldSchema.label,
              type: fieldSchema.type,
              searchable: fieldSchema.searchable || false,
            };
            if (fieldSchema.customRender) {
              column.render = (row, index) => fieldSchema.customRender(row, index);
            }
            return column;
          })}
        overrideRows={tableData}
      />
    </>
  );
};

CustomPage.propTypes = {
  dataListName: PropTypes.string.isRequired,
  schema: PropTypes.object.isRequired,
  title: PropTypes.string,
  description: PropTypes.string,
  searchable: PropTypes.bool,
  customAddElement: PropTypes.element,
  ROWS_PER_PAGE: PropTypes.number,
  tableData: PropTypes.array,
};

export default CustomPage;
