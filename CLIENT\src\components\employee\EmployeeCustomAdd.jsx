import React, { useState } from "react";
import {
  <PERSON>ton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
} from "@mui/material";
import { useForm } from "react-hook-form";
import CustomTextField from "../../global/components/CustomTextField";
import CustomAutoComplete from "../../global/components/CustomAutoComplete";
import CustomButton from "../../global/components/CustomButton";
import api from "../../config/api";
import { toast } from "react-hot-toast";

const AddEmployeeDialog = ({ parentClose }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [isImportOpen, setIsImportOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [stepOptions, setStepOptions] = useState([]);
  const [salaryData, setSalaryData] = useState(null);
  const [status, setStatus] = useState("Active");

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    trigger,
    getValues,
    formState: { isDirty },
  } = useForm({
    defaultValues: {
      EmployeeID: "",
      EmployeeFullName: "",
      Region: "",
      Department: "",
      PositionTitle: "",
      StatusOfAppointment: "",
      SG: "",
      JG: "",
      Step: "",
      Rate: "",
      Status: "Active",
    },
  });

  const handleOpen = () => setIsOpen(true);
  const handleClose = () => {
    setIsOpen(false);
    parentClose && parentClose();
  };

  const handleImportOpen = () => setIsImportOpen(true);
  const handleImportClose = () => setIsImportOpen(false);

  const handleFileChange = (event) => {
    setSelectedFile(event.target.files[0]);
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error("Please select a file");
      return;
    }

    setLoading(true);
    const formData = new FormData();
    formData.append("file", selectedFile);

    try {
      const response = await api.post("/api/upload", formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });

      toast.success(response.data.message);
      handleImportClose();
    } catch (error) {
      toast.error(error.response?.data?.error || "File upload failed");
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (data) => {
    setLoading(true);
    try {
      await api.post("/employees", data);
      toast.success("Employee added successfully");
      handleClose();
    } catch (error) {
      toast.error(error.response?.data?.error || "Failed to save employee");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div style={{ display: "flex", gap: "10px" }}>
        <Button variant="contained" size="large" onClick={handleOpen}>
          Add Employee
        </Button>
        <Button
          variant="contained"
          color="secondary"
          size="large"
          onClick={handleImportOpen}
        >
          Import Employee
        </Button>
      </div>

      <Dialog open={isOpen} onClose={handleClose}>
        <DialogTitle>Add New Employee</DialogTitle>
        <DialogContent dividers>
          <Typography variant="body2" gutterBottom>
            Fill in the employee details.
          </Typography>

          <CustomTextField
            fieldName="EmployeeID"
            control={control}
            label="Employee ID"
            sx={{ mb: 2 }}
            required
          />
          <CustomTextField
            fieldName="EmployeeFullName"
            control={control}
            label="Employee Name"
            required
          />

          <CustomAutoComplete
            fieldName="Region"
            label="Select Region"
            apiList={{
              api: api,
              endpoint: "/regions",
              listName: "regions",
            }}
            control={control}
            sx={{ mb: 2, mt: 0 }}
            getOptionLabel="Region"
            required
          />

          <CustomAutoComplete
            fieldName="Department"
            label="Select Department"
            apiList={{
              api: api,
              endpoint: "/departments",
              listName: "departments",
            }}
            control={control}
            sx={{ mb: 2, mt: 0 }}
            getOptionLabel="Department"
            required
          />

          <CustomAutoComplete
            fieldName="PositionTitle"
            label="Select Position"
            apiList={{
              api: api,
              endpoint: "/positions",
              listName: "positions",
            }}
            control={control}
            getOptionLabel="PositionTitle"
            required
          />

          <CustomAutoComplete
            fieldName="StatusOfAppointment"
            label="Select Status of Appointment"
            apiList={{
              api: api,
              endpoint: "/status",
              listName: "status",
            }}
            control={control}
            sx={{ mb: 2 }}
            getOptionLabel="StatusOfAppointment"
            required
          />

          <CustomAutoComplete
            fieldName="Status"
            label="Select Status"
            options={["Active", "Inactive"]}
            control={control}
            sx={{ mb: 2 }}
            getOptionLabel={(option) => option}
            required
            onChange={(value) => {
              setStatus(value);
              setValue("Status", value);
            }}
          />

          <CustomTextField
            fieldName="SG"
            control={control}
            label="Salary Grade"
            type="number"
            sx={{ mb: 2 }}
            onChange={async (e) => {
              const sgValue = e.target.value;
              setValue("SG", sgValue);

              try {
                const response = await api.get(
                  `salary-grades?salary_grade=${sgValue}`
                );
                const salaryGrades = response.data.grades?.[0];

                if (salaryGrades) {
                  setValue("JG", salaryGrades.job_grade);
                  const steps = salaryGrades.steps.map((aw) => aw + "");
                  setStepOptions(steps);
                  setSalaryData(salaryGrades);
                  const defaultStep = steps[0];
                  setValue("Step", defaultStep);
                  const rate = salaryGrades.rates[defaultStep];
                  setValue("Rate", rate);
                } else {
                  setValue("JG", "");
                  setStepOptions([]);
                  setSalaryData(null);
                  setValue("Step", "");
                  setValue("Rate", "");
                }
              } catch (error) {
                console.error("Error fetching salary grades:", error);
              }
            }}
          />
          <CustomTextField
            fieldName="JG"
            control={control}
            label="Job Grade"
            type="number"
            sx={{ mb: 2 }}
            onChange={async (e) => {
              const jgValue = e.target.value;
              setValue("JG", jgValue);

              if (!getValues("SG")) {
                const stepValue = getValues("Step") || "1";
                try {
                  const response = await api.get(
                    `/rate-by-job-grade-and-step/${jgValue}/${stepValue}`
                  );
                  const rateData = response.data;

                  if (rateData) {
                    setValue("Rate", rateData.rate);
                  } else {
                    setValue("Rate", "");
                    console.error(
                      "Rate not found for the given job grade and step"
                    );
                  }
                } catch (error) {
                  console.error(
                    "Error fetching rate by job grade and step:",
                    error
                  );
                }
              }

              trigger("Rate");
            }}
          />

          <CustomAutoComplete
            fieldName="Step"
            options={stepOptions}
            defaultOptions={["1", "2", "3", "4", "5", "6", "7", "8"]}
            label="Select Step"
            control={control}
            sx={{ mb: 2 }}
            onChange={async (value) => {
              setValue("Step", value);

              if (salaryData) {
                const rate = salaryData.rates[value] || "";
                setValue("Rate", rate);
              } else {
                const jgValue = getValues("JG");
                if (jgValue) {
                  try {
                    const response = await api.get(
                      `/rate-by-job-grade-and-step/${jgValue}/${value}`
                    );
                    const rateData = response.data;

                    if (rateData) {
                      setValue("Rate", rateData.rate);
                    } else {
                      setValue("Rate", "");
                      console.error(
                        "Rate not found for the given job grade and step"
                      );
                    }
                  } catch (error) {
                    console.error(
                      "Error fetching rate by job grade and step:",
                      error
                    );
                  }
                }
              }

              trigger("Rate");
            }}
          />

          <CustomTextField
            fieldName="Rate"
            control={control}
            label="Rate"
            type="number"
            sx={{ mb: 2 }}
          />
        </DialogContent>

        <DialogActions>
          <CustomButton
            loading={loading}
            plain
            color="error"
            onClick={handleClose}
          >
            Close
          </CustomButton>
          <CustomButton
            loading={loading}
            onClick={handleSubmit(handleSave)}
          >
            Create
          </CustomButton>
        </DialogActions>
      </Dialog>

      <Dialog open={isImportOpen} onClose={handleImportClose}>
        <DialogTitle>Import Employees</DialogTitle>
        <DialogContent dividers>
          <Typography variant="body2" gutterBottom>
            Upload an Excel file to import employees.
          </Typography>
          <input type="file" accept=".xlsx,.xls" onChange={handleFileChange} />
        </DialogContent>
        <DialogActions>
          <CustomButton
            loading={loading}
            plain
            color="error"
            onClick={handleImportClose}
          >
            Cancel
          </CustomButton>
          <CustomButton loading={loading} onClick={handleUpload}>
            Upload
          </CustomButton>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default AddEmployeeDialog;