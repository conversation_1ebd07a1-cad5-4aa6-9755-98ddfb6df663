import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TablePagination,
  Checkbox,
  IconButton,
  Popover,
  TextField,
  Box,
  Chip,
  Switch,
} from "@mui/material";
import FilterListIcon from "@mui/icons-material/FilterList";
import api from "../../config/api";
import PropTypes from "prop-types";

const CustomTable = ({
  ROWS_PER_PAGE,
  dataListName,
  apiPath,
  columns,
  searchQuery = "",
}) => {
  const [employees, setEmployees] = useState([]);
  const [page, setPage] = useState(0);
  const [totalRecords, setTotalRecords] = useState(0);
  const [loading, setLoading] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [filters, setFilters] = useState(
    columns.reduce((acc, col) => {
      acc[col.field] = "";
      return acc;
    }, {})
  );
  const [anchorEl, setAnchorEl] = useState(null);
  const [activeFilterField, setActiveFilterField] = useState(null);

  const fetchData = async () => {
    setLoading(true);
    try {
      const queryParams = {
        page: page + 1,
        limit: ROWS_PER_PAGE,
        search: searchQuery,
        ...Object.entries(filters).reduce((acc, [key, value]) => {
          if (value && key !== "select" && key !== "action") {
            acc[key] = value;
          }
          return acc;
        }, {}),
      };

      const response = await api.get(apiPath, { params: queryParams });
      setEmployees(response.data.employees);
      setTotalRecords(response.data.totalRecords);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [page, ROWS_PER_PAGE, apiPath, refreshTrigger, searchQuery, filters]);

  const handleFilterClick = (event, field) => {
    setAnchorEl(event.currentTarget);
    setActiveFilterField(field);
  };

  const handleFilterClose = () => {
    setAnchorEl(null);
    setActiveFilterField(null);
  };

  const handleFilterChange = (field, value) => {
    setFilters((prev) => ({ ...prev, [field]: value }));
    setPage(0); // Reset to first page on filter change
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      const activeEmployees = employees
        .filter((row) => row.employeeStatus === "Active")
        .map((row) => row._id);
      setSelectedRows(activeEmployees);
    } else {
      setSelectedRows([]);
    }
  };

  const handleSelectRow = (id, checked) => {
    if (checked) {
      setSelectedRows((prev) => [...prev, id]);
    } else {
      setSelectedRows((prev) => prev.filter((rowId) => rowId !== id));
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
    setSelectedRows([]);
  };

  const handleRefresh = () => {
    setRefreshTrigger((prev) => prev + 1);
  };

  const handleStatusToggle = async (id, checked) => {
    const newStatus = checked ? "Active" : "Inactive";
    try {
      await api.put(`/employees/${id}`, { employeeStatus: newStatus });
      handleRefresh();
      return true;
    } catch (error) {
      console.error("Error updating status:", error);
      return false;
    }
  };

  const open = Boolean(anchorEl);
  const id = open ? "filter-popover" : undefined;

  return (
    <TableContainer component={Paper} sx={{ maxHeight: "60vh", overflow: "auto" }}>
      <Table>
        <TableHead sx={{ position: "sticky", top: 0, backgroundColor: "#375e38", zIndex: 1 }}>
          <TableRow>
            {columns.map((column) => (
              <TableCell 
                key={column.field} 
                sx={{ color: "#fff" }}
              >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  {column.field === "select" ? (
                    <Checkbox
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      indeterminate={
                        selectedRows.length > 0 &&
                        selectedRows.length <
                          employees.filter((row) => row.employeeStatus === "Active").length
                      }
                      checked={
                        selectedRows.length ===
                        employees.filter((row) => row.employeeStatus === "Active").length
                      }
                      disabled={employees.length === 0}
                    />
                  ) : (
                    column.label
                  )}
                  {column.field !== "select" && column.field !== "action" && (
                    <IconButton
                      size="small"
                      onClick={(e) => handleFilterClick(e, column.field)}
                      sx={{ ml: 1, color: "#fff" }}
                    >
                      <FilterListIcon />
                    </IconButton>
                  )}
                </Box>
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {loading ? (
            <TableRow>
              <TableCell colSpan={columns.length}>Loading...</TableCell>
            </TableRow>
          ) : (
            employees.map((row) => (
              <TableRow key={row._id}>
                {columns.map((column) => (
                  <TableCell key={column.field}>
                    {column.field === "select" ? (
                      <Checkbox
                        checked={selectedRows.includes(row._id)}
                        onChange={(e) =>
                          handleSelectRow(row._id, e.target.checked)
                        }
                        disabled={row.employeeStatus === "Inactive"}
                      />
                    ) : column.field === "employeeStatus" ? (
                      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                        <Chip 
                          label={row.employeeStatus || "N/A"} 
                          color={row.employeeStatus === "Active" ? "success" : "error"}
                          size="small"
                        />
                        <Switch
                          checked={row.employeeStatus === "Active"}
                          onChange={(e) => handleStatusToggle(row._id, e.target.checked)}
                          size="small"
                        />
                      </Box>
                    ) : column.render ? (
                      (() => {
                        const renderResult = column.render(row);
                        if (renderResult && typeof renderResult === "object" && "render" in renderResult) {
                          const { render, onChange } = renderResult;
                          return React.cloneElement(render, {
                            onChange: async (e) => {
                              const success = await onChange(e.target.checked);
                              if (success) handleRefresh();
                            },
                          });
                        }
                        return renderResult;
                      })()
                    ) : (
                      row[column.field] || ""
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleFilterClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
      >
        <Box sx={{ p: 2, width: 200 }}>
          <TextField
            size="small"
            label={`Filter ${columns.find((col) => col.field === activeFilterField)?.label}`}
            value={filters[activeFilterField] || ""}
            onChange={(e) => handleFilterChange(activeFilterField, e.target.value)}
            fullWidth
          />
        </Box>
      </Popover>
      <TablePagination
        rowsPerPageOptions={[ROWS_PER_PAGE]}
        component="div"
        count={totalRecords}
        rowsPerPage={ROWS_PER_PAGE}
        page={page}
        onPageChange={handleChangePage}
      />
    </TableContainer>
  );
};

CustomTable.propTypes = {
  ROWS_PER_PAGE: PropTypes.number.isRequired,
  dataListName: PropTypes.string.isRequired,
  apiPath: PropTypes.string.isRequired,
  columns: PropTypes.arrayOf(
    PropTypes.shape({
      field: PropTypes.string.isRequired,
      label: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),
      type: PropTypes.string,
      searchable: PropTypes.bool,
      render: PropTypes.func,
    })
  ).isRequired,
  searchQuery: PropTypes.string,
};

export default CustomTable;
