import React, { useState, useEffect } from "react";
import CustomMenu from "./PersonnelCustomMenu";
import CustomTable from "./PersonnelCustomTable";
import PropTypes from "prop-types";
import {
  Button,
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  CircularProgress,
} from "@mui/material";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import api from "../../config/api";
import { useUser } from "../../context/UserContext";
import { useQueryClient } from "@tanstack/react-query";

// Define allowed columns for COS (adjust as needed)
const allowedCOSColumns = [
  "department",
  "region",
  // "budgetType",
  // "processBy",
  // "processDate",
  // "fiscalYear",
  "positionTitle",
  "gradelevel_SG",
  "step",
  "gradelevel_JG",
  "employeeFullName",
  "employeeNumber",
  "division",
  "statusOfAppointment",
  "monthlySalary",
  "annualSalary",
  "Total",
];

const PSCOSCustomPageTable = ({
  dataListName,
  schema,
  title = "",
  description = "",
  hasEdit = true,
  hasDelete = true,
  customEditElement = null,
  additionalMenuOptions = [],
  ROWS_PER_PAGE = 20,
}) => {
  const [openDialog, setOpenDialog] = useState(false);
  const [loading, setLoading] = useState(false);
  const [fiscalYear, setFiscalYear] = useState("");
  const [budgetType, setBudgetType] = useState("");
  const apiPath = `/${dataListName}`;
  const { currentUser } = useUser();
  const queryClient = useQueryClient();

  // Fetch active settings
  const fetchActiveSettings = async () => {
    try {
      const response = await api.get("/settings/active");
      if (response.data) {
        setFiscalYear(response.data.fiscalYear || "");
        setBudgetType(response.data.budgetType || "");
      }
    } catch (error) {
      console.error("Error fetching active settings:", error);
    }
  };

  useEffect(() => {
    fetchActiveSettings();
  }, []);

  // Bulk add COS personnel – gamit ang react-query invalidation para mag-refetch agad
  const handleAddAllPersonnel = async (statusOfAppointment) => {
    if (!fiscalYear) {
      toast.error("Active fiscal year not set. Please try again later.");
      return;
    }
    setLoading(true);
    try {
      const processBy = `${currentUser.FirstName} ${currentUser.LastName}`;
      const response = await api.post(`/cos-personnel/bulk-add`, {
        processBy,
        statusOfAppointment,
        fiscalYear,
        budgetType,
      });
      if (Array.isArray(response.data)) {
        toast.success("Personnel successfully added!");
        // Invalidate query para mapilitang mag-refetch ang table data
        queryClient.invalidateQueries([dataListName]);
      } else {
        toast.error("Error: Bulk add response invalid");
      }
    } catch (error) {
      if (error.response?.status === 403) {
        toast.error(error.response.data.message || "Submission is locked.");
      } else {
        toast.error("Error bulk adding personnel: " + error.message);
      }
    } finally {
      setLoading(false);
      setOpenDialog(false);
    }
  };

  const handleOpenDialog = () => setOpenDialog(true);
  const handleCloseDialog = () => setOpenDialog(false);

  return (
    <>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <Box display="flex" alignItems="center" >
          <Button
             variant="contained"
             color="primary"
             onClick={handleOpenDialog}
             sx={{
               mr: 2,
               background: "#009688",
               color: "#fff",
             //  border: "thin solid #fff",
               "&:hover": {
                 background: "#00796B",
                 color: "#fff",
                textDecoration: "underline rgb(255, 255, 255)"
               },
             }}
            disabled={!fiscalYear}
          >
            Add COS Personnel
          </Button>
        </Box>
      </Box>

      <Dialog open={openDialog} onClose={handleCloseDialog}>
        <DialogTitle>Confirm Bulk Add</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to add all personnel with a status of
            appointment as COS?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} color="primary">
            Cancel
          </Button>
          <Button
            onClick={() => handleAddAllPersonnel("COS")}
            color="primary"
            autoFocus
          >
            {loading ? <CircularProgress size={24} /> : "Yes"}
          </Button>
        </DialogActions>
      </Dialog>

      <ToastContainer />

      {/* Hindi na natin pinapasa ang rows; hayaan na lang ang CustomTable na mag-fetch gamit ang react-query */}
      <CustomTable
        ROWS_PER_PAGE={ROWS_PER_PAGE}
        dataListName={dataListName}
        apiPath={apiPath}
        columns={Object.keys(schema)
          .filter((key) => allowedCOSColumns.includes(key) || key === "action")
          .map((key) => {
            const fieldSchema = schema[key];
            const column = {
              field: key,
              label: fieldSchema.label,
              type: fieldSchema.type,
              searchable: fieldSchema.searchable || false,
              textAlign: fieldSchema.alignRight ? "right" : "left",
            };

            if (fieldSchema.type === "action") {
              column.render = (row) => (
                <CustomMenu
                  additionalMenuOptions={additionalMenuOptions}
                  customEditElement={customEditElement}
                  hasEdit={hasEdit}
                  hasDelete={hasDelete}
                  row={row}
                  schema={schema}
                  endpoint={apiPath}
                  dataListName={dataListName}
                  activeFiscalYear={fiscalYear}
                />
              );
            }
            if (fieldSchema.customRender) {
              column.render = (row) => fieldSchema.customRender(row);
            }
            return column;
          })}
      />
    </>
  );
};

PSCOSCustomPageTable.propTypes = {
  dataListName: PropTypes.string.isRequired,
  schema: PropTypes.objectOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      type: PropTypes.string.isRequired,
      show: PropTypes.bool,
      searchable: PropTypes.bool,
      customRender: PropTypes.func,
      default: PropTypes.any,
      alignRight: PropTypes.bool,
    })
  ).isRequired,
  hasEdit: PropTypes.bool,
  hasDelete: PropTypes.bool,
  customEditElement: PropTypes.element,
  additionalMenuOptions: PropTypes.array,
  ROWS_PER_PAGE: PropTypes.number,
};

export default PSCOSCustomPageTable;
