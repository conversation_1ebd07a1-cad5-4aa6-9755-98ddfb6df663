import {
  Box,
  Button,
  IconButton,
  MenuItem,
  Paper,
  Popover,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableHead,
  TablePagination,
  TableRow,
  TableSortLabel,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import { blueGrey, green, grey } from "@mui/material/colors";
import { useQuery } from "@tanstack/react-query";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import { TiFilter } from "react-icons/ti";
import api from "../../config/api";
import { useSearch } from "../../context/SearchContext";
import TableBodyLoading from "../../global/components/TableBodyLoading";
import TextSearchable from "../../global/components/TextSearchable";
import formatCurrency from "../../utils/formatCurrency";
import { formatDateToMDY } from "../../utils/formatDate";

const COSCustomTable = ({
  columns,
  ROWS_PER_PAGE = 20,
  apiPath,
  dataListName = "",
  orderByDefault = "updatedAt",
  onDataChange,
}) => {
  const { searchValue } = useSearch();
  const TEN_SECONDS_AGO = dayjs().subtract(10, "second");

  const [order, setOrder] = useState("desc");
  const [orderBy, setOrderBy] = useState(orderByDefault);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(ROWS_PER_PAGE);
  const [filterAnchorEl, setFilterAnchorEl] = useState(null);
  const [focusedCell, setFocusedCell] = useState(null);
  const [fieldAndValue, setFieldAndValue] = useState({
    field: "",
    value: "",
    label: "",
    operator: "=",
  });
  const [totals, setTotals] = useState({});

  // Build params object
  const params = {
    page: page + 1,
    limit: rowsPerPage,
    orderBy,
    order,
    operator: fieldAndValue.operator,
    statusOfAppointment: "COS",
  };
  if (searchValue) params.search = searchValue;
  if (fieldAndValue.field) params[fieldAndValue.field] = fieldAndValue.value;

  const { data, isLoading, refetch } = useQuery({
    queryKey: [apiPath, page, rowsPerPage, searchValue, fieldAndValue.value, orderBy, order],
    queryFn: async () => {
      const res = await api.get(apiPath, { params });
      return res.data;
    },
  });

  useEffect(() => {
    if (data && data[dataListName]) {
      const newTotals = columns.reduce((acc, column) => {
        if (column.type === "number") {
          acc[column.field] = data[dataListName].reduce(
            (sum, row) => sum + (row[column.field] || 0),
            0
          );
        }
        return acc;
      }, {});
      setTotals(newTotals);
      if (onDataChange) {
        onDataChange(data[dataListName]);
      }
    }
  }, [data, columns, dataListName, onDataChange]);

  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
  };

  const handleFilterClick = (event, columnKey, columnLabel) => {
    setFilterAnchorEl(event.currentTarget);
    if (fieldAndValue.field !== columnKey)
      setFieldAndValue({ field: columnKey, value: "", label: columnLabel });
  };

  const handleFilterClearValue = () =>
    setFieldAndValue((prev) => ({ ...prev, value: "" }));
  const handleFilterClose = () => setFilterAnchorEl(null);

  const handleCellClick = (rowIndex, columnKey, _id) => {
    setFocusedCell({ rowIndex, columnKey, _id });
  };

  const handleChangePage = (event, newPage) => setPage(newPage);
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

const getTotalAmount = async () => {
  try {
    const response = await api.get("/grandtotalCOS");
    return response.data.grandTotal || 0;
  } catch (error) {
    console.error("Error fetching grand total:", error);
    return 0;
  }
};


const [grandTotal, setGrandTotal] = useState(0);

useEffect(() => {
  const fetchGrandTotal = async () => {
    const totalAmount = await getTotalAmount();
    setGrandTotal(totalAmount);
  };
  fetchGrandTotal();
}, []);

  const handleDateChange = (e) => {
    const inputValue = e.target.value;
    if (!inputValue) return;
    const [year, month, day] = inputValue.split("-");
    const formattedValue = `${month}-${day}-${year}`;
    setFieldAndValue((prev) => ({ ...prev, value: formattedValue }));
  };

  const getFormattedValue = () => {
    if (!fieldAndValue.value) return "";
    const [month, day, year] = fieldAndValue.value.split("-");
    return `${year}-${month}-${day}`;
  };

  const renderFilter = () => {
    const column = columns.find((col) => col.field === fieldAndValue.field);
    if (column) {
      if (column.type === "date") {
        return (
          <>
            <TextField
              size="small"
              type="date"
              value={getFormattedValue()}
              onChange={handleDateChange}
              fullWidth
              sx={{ "& .MuiOutlinedInput-root": { borderRadius: "8px" } }}
            />
            {fieldAndValue.value && (
              <Button size="small" variant="contained" color="error" onClick={handleFilterClearValue} sx={{ my: 1 }}>
                Clear
              </Button>
            )}
          </>
        );
      } else if (column.type === "number") {
        return (
          <>
            <Select
              size="small"
              value={fieldAndValue.operator || "="}
              onChange={(e) => setFieldAndValue((prev) => ({ ...prev, operator: e.target.value }))}
              fullWidth
            >
              <MenuItem value="=">Equal to</MenuItem>
              <MenuItem value="<">Less than</MenuItem>
              <MenuItem value=">">Greater than</MenuItem>
              <MenuItem value="<=">Less than or Equal</MenuItem>
              <MenuItem value=">=">Greater than or Equal</MenuItem>
            </Select>
            <TextField
              size="small"
              type="number"
              placeholder={`Enter ${fieldAndValue.label}`}
              value={fieldAndValue.value || ""}
              onChange={(e) => setFieldAndValue((prev) => ({ ...prev, value: e.target.value }))}
              fullWidth
            />
            {fieldAndValue.value && (
              <Button size="small" variant="contained" color="error" onClick={handleFilterClearValue} sx={{ my: 1 }}>
                Clear
              </Button>
            )}
          </>
        );
      }
    }
    return (
      <>
        <TextField
          size="small"
          placeholder={`Search by ${fieldAndValue.label}`}
          value={fieldAndValue.value}
          onChange={(e) => setFieldAndValue((prev) => ({ ...prev, value: e.target.value }))}
          fullWidth
        />
        {fieldAndValue.value && (
          <Button size="small" variant="contained" color="error" onClick={handleFilterClearValue} sx={{ my: 1 }}>
            Clear
          </Button>
        )}
      </>
    );
  };

  const tableData = data && data[dataListName] ? data[dataListName] : [];

  return (
    <>
    <Box overflow="auto" sx={{ border: "solid thin #fff" }}>
      <Paper sx={{ width: "100%", overflow: "hidden", borderRadius: 0 }}>
        <TableContainer sx={{ height: "60vh" }}>
          <Table size="small" sx={{ borderCollapse: "collapse" }}>
            <TableHead>
              <TableRow>
                {columns.map((column) =>
                  column.type === "action" ? (
                    <TableCell key={column.field} sx={{ position: "sticky", top: 0, backgroundColor: "#375e38", zIndex: 1, color: "#fff" }}>
                      {column.label}
                    </TableCell>
                  ) : (
                    <TableCell
                      key={column.field}
                      sx={{
                        position: "sticky",
                        top: 0,
                        backgroundColor: "#375e38",
                        zIndex: 1,
                        borderRight: "1px solid",
                        borderColor: grey[500],
                        color: "#fff",
                        textAlign: column.type === "number" ? "right" : "left",
                      }}
                    >
                      <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                        <TableSortLabel
                          active={orderBy === column.field}
                          direction={orderBy === column.field ? order : "asc"}
                          onClick={() => handleRequestSort(column.field)}
                          sx={{ flex: 1, color: "#fff" }}
                        >
                          {column.label}
                        </TableSortLabel>
                        <Tooltip title={`Filter ${column.label}`}>
                          <IconButton size="small" onClick={(event) => handleFilterClick(event, column.field, column.label)}>
                            <TiFilter color="lightgray" />
                          </IconButton>
                        </Tooltip>
                      </div>
                    </TableCell>
                  )
                )}
              </TableRow>
            </TableHead>
            {isLoading ? (
              <TableBodyLoading numCell={columns.length} />
            ) : (
              <TableBody>
                {tableData.length === 0 ? (
                  <TableRow sx={{ height: "70vh" }}>
                    <TableCell colSpan={columns.length} sx={{ textAlign: "center", fontWeight: "500" }}>
                      {searchValue ? (
                        <>No results found for <b>"{searchValue}"</b>.</>
                      ) : (
                        "No rows found."
                      )}
                    </TableCell>
                  </TableRow>
                ) : (
                  tableData.map((row, rowIndex) => {
                    const isRecentlyUpdated =
                      row.updatedAt && dayjs(row.updatedAt).isAfter(TEN_SECONDS_AGO);
                    return (
                      <TableRow
                        key={row._id || rowIndex}
                        sx={{
                          backgroundColor: isRecentlyUpdated
                            ? green[50]
                            : rowIndex % 2 === 0
                            ? blueGrey[50]
                            : "#ffffff",
                        }}
                      >
                        {columns.map((column, i) => (
                          <TableCell
                            key={column.field}
                            onClick={() => handleCellClick(rowIndex, column.field, row["_id"])}
                            sx={{
                              maxWidth: column.field === "employeeName" ? "none" : "800px",
                              whiteSpace: column.field === "employeeName" ? "normal" : "nowrap",
                              overflow: "hidden",
                              textOverflow: "ellipsis",
                              fontWeight: "500",
                              textAlign: column.type === "number" ? "right" : "left",
                              ...(focusedCell &&
                                focusedCell._id === row["_id"] &&
                                focusedCell.rowIndex === rowIndex &&
                                focusedCell.columnKey === column.field && {
                                  outline: "2px solid lightblue",
                                }),
                              borderLeft:
                                i !== 0
                                  ? rowIndex % 2 === 0
                                    ? "1px solid white"
                                    : `1px solid ${grey[200]}`
                                  : undefined,
                            }}
                          >
                            {column.render ? (
                              column.render(row)
                            ) : !column.searchable ? (
                              column.type === "date" ? (
                                formatDateToMDY(row[column.field])
                              ) : column.type === "number" ? (
                                formatCurrency(row[column.field])
                              ) : column.type === "boolean" ? (
                                row[column.field] ? "Yes" : "No"
                              ) : (
                                row[column.field]
                              )
                            ) : (
                              <TextSearchable
                                columnName={
                                  column.type === "date"
                                    ? formatDateToMDY(row[column.field])
                                    : column.type === "number"
                                    ? formatCurrency(row[column.field])
                                    : column.type === "boolean"
                                    ? row[column.field]
                                      ? "Yes"
                                      : "No"
                                    : row[column.field]
                                }
                              />
                            )}
                          </TableCell>
                        ))}
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            )}
            {/* <TableFooter>
              <TableRow>
                {columns.map((column) => (
                  <TableCell
                    key={column.field}
                    sx={{
                      position: "sticky",
                      bottom: 0,
                      backgroundColor: "#f0f5f5",
                      zIndex: 1,
                      fontWeight: "800",
                      fontSize: 14,
                      textAlign: column.type === "number" ? "right" : "left",
                    }}
                  >
                    {column.type === "number" ? formatCurrency(totals[column.field]) : ""}
                  </TableCell>
                ))}
              </TableRow>
            </TableFooter> */}
          </Table>
        </TableContainer>
      </Paper>
      {!isLoading ? (
        <TablePagination
          rowsPerPageOptions={[10, ROWS_PER_PAGE, 50]}
          component="div"
          count={data && data.totalRecords ? data.totalRecords : 0}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          sx={{
            "& .MuiTablePagination-root, & .MuiTablePagination-toolbar, & .MuiTablePagination-selectLabel, & .MuiTablePagination-input, & .MuiTablePagination-displayedRows":
              {
                color: "#fff",
                fontweight: 1000,
                fontSize: 14,
              },
          }}
      
        />
      ) : null}
      <Popover
        open={Boolean(filterAnchorEl)}
        anchorEl={filterAnchorEl}
        onClose={handleFilterClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
      >
        <Box sx={{ display: "flex", flexDirection: "column", gap: 1, p: 1 }}>
          <Box sx={{ fontSize: 14, fontWeight: 600, color: "#333" }}>
            Filter by {fieldAndValue.label}
          </Box>
          {renderFilter()}
        </Box>
      </Popover>
    </Box>
 <Box
 sx={{
   display: "flex",
   alignItems: "center",
   mt: 1,
   gap: 1,
   justifyContent: "flex-end",
 }}
>
 <Typography
   sx={{
     color: "#fff",
     fontWeight: "bold",
     textAlign: "right",
   }}
 >
   TOTAL :
 </Typography>
 <Typography
   sx={{
     color: "#fff",
     fontWeight: "bold",
     textAlign: "right",
   }}
 >
   {grandTotal.toLocaleString(undefined, {
     minimumFractionDigits: 2,
   })}
 </Typography>
</Box>
   </>
  );
};

export default COSCustomTable;
