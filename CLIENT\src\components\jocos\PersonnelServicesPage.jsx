import React from "react";
import { Box } from "@mui/material";
import CustomPageTable from "./PersonnelCustomPageTable";

const COSPersonnelServicesPage = () => {
  // I-define ang table schema
  const cospersonnelSchema = {
    action: { type: "action", label: "ACTIONS" },
    department: { type: "text", label: "DEPARTMENT", show: true },
    region: { type: "text", label: "REGION", show: true },
    budgetType: { type: "text", label: "BUDGET TYPE" },
    processBy: { type: "text", label: "PROCESSED BY" },
    processDate: { type: "date", label: "PROCESS DATE" },
    fiscalYear: { type: "text", label: "FISCAL YEAR"},
    positionTitle: { type: "text", label: "POSITION TITLE", searchable: true, show: true },
    gradelevel_SG: { type: "text", label: "SG", show: true },
    step: { type: "number", label: "STEP", show: true },
    gradelevel_JG: { type: "text", label: "JG", show: true },
    employeeFullName: { type: "text", label: "EMPLOYEE NAME", searchable: true, show: true },
    employeeNumber: { type: "text", label: "EMPLOYEE NUMBER", show: true },
    division: { type: "text", label: "DIVISION", show: true },
    statusOfAppointment: { type: "text", label: "STATUS OF APPOINTMENT", show: true },
    monthlySalary: { type: "number", label: "MONTHLY SALARY", show: true },
    annualSalary: { type: "number", label: "ANNUAL SALARY", show: true },
    Total: { type: "number", label: "TOTAL", show: true }
  };

  return (
   
      
      <CustomPageTable
        dataListName="cosPersonnels"
        title="COS Personnel Services"
        description="This is the COS Personnel Services Table"
        schema={cospersonnelSchema}
        searchable={true}
        hasEdit={true}
        hasDelete={false}
        ROWS_PER_PAGE={10}
        hideHeader={true} // siguraduhin na tinatanggap ito ng iyong table component
      />
   
  );
};

export default COSPersonnelServicesPage;
