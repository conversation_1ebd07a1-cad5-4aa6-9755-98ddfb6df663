import React, { useEffect, useState, useMemo } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  TextField,
  Autocomplete,
  MenuItem,
  CircularProgress,
  Typography,
  Box,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import CustomButton from "../../global/components/CustomButton";
import api from "../../config/api";
import { useUser } from "../../context/UserContext";

const MealAllowanceDialog = ({ row, endpoint, dataListName }) => {
  const isEditing = Boolean(row);
  const [open, setOpen] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [formData, setFormData] = useState(null);
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { currentUser } = useUser();
  const queryClient = useQueryClient();

  const { control, handleSubmit, reset, watch, setValue, formState: { errors } } = useForm({
    defaultValues: {
      employee: null,
      actualDays: 22, // Fixed to 22 days
      positionTitle: "",
      department: "",
      division: "",
      region: "",
    },
  });

  const actualDays = watch("actualDays");
  const selectedEmployee = watch("employee");

  // Fetch settings with polling to detect changes
  const { data: settings, isLoading: settingsLoading } = useQuery({
    queryKey: ["activeSettings"],
    queryFn: async () => {
      const res = await api.get("/settings/active");
      return res.data;
    },
    refetchInterval: 60000, // Poll every 60 seconds
  });

  useEffect(() => {
    if (isEditing && row) {
      reset({
        employee: {
          employeeNumber: row.employeeNumber,
          employeeFullName: row.employeeFullName,
          _id: row._id,
          positionTitle: row.positionTitle,
          department: row.department,
          division: row.division,
          region: row.region,
        },
        actualDays: row.actualDays || 0,
        positionTitle: row.positionTitle || "",
        department: row.department || "",
        division: row.division || "",
        region: row.region || "",
      });
    }
  }, [isEditing, row, reset]);

  useEffect(() => {
    if (selectedEmployee && !isEditing) {
      setValue("positionTitle", selectedEmployee.positionTitle || "", { shouldValidate: true });
      setValue("department", selectedEmployee.department || "", { shouldValidate: true });
      setValue("division", selectedEmployee.division || "", { shouldValidate: true });
      setValue("region", selectedEmployee.region || "", { shouldValidate: true });
    }
  }, [selectedEmployee, setValue, isEditing]);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        const res = await api.get("/getpersonnels/hiredBeforeJune1988");
        setEmployees(res.data);
      } catch (err) {
        setError(err.message || "Failed to fetch employees");
        toast.error(err.message || "Failed to fetch employees");
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  const computeAmount = useMemo(() => {
    if (!settings || !settings.meal || actualDays === undefined) return 0;
    // Calculate monthly amount first
    const monthlyAmount = Number(settings.meal) * Number(actualDays);
    // Return annual amount (monthly × 12)
    return (monthlyAmount * 12).toFixed(2);
  }, [settings, actualDays]);

  const mutation = useMutation({
    mutationFn: async (data) => {
      if (!data.employee) throw new Error("Employee is required");
      if (!Number.isInteger(Number(data.actualDays))) throw new Error("Actual days must be an integer");

      const payload = {
        employeeNumber: data.employee.employeeNumber,
        employeeFullName: data.employee.employeeFullName,
        positionTitle: data.positionTitle,
        department: data.department,
        division: data.division,
        region: data.region,
        actualDays: Number(data.actualDays),
        amount: computeAmount,
        processBy: `${currentUser.FirstName} ${currentUser.LastName}`,
        processDate: new Date(),
        fiscalYear: settings?.fiscalYear,
        budgetType: settings?.budgetType,
      };

      return isEditing
        ? api.put(`${endpoint}/${row._id}`, payload)
        : api.post(endpoint, payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries([dataListName]);
      toast.success(isEditing ? "Record updated successfully" : "Record created successfully");
      handleClose();
    },
    onError: (err) => {
      toast.error(err.response?.data?.message || err.message || "Something went wrong");
    },
  });

  const deleteMutation = useMutation({
    mutationFn: (id) => api.delete(`/meal-allowance/${id}`),
    onSuccess: (response) => {
      toast.success("Meal allowance deleted successfully");
      
      // Invalidate meal allowances queries
      queryClient.invalidateQueries({ queryKey: ["meal-allowance"] });
      
      // Force invalidate personnel services queries
      queryClient.invalidateQueries({ queryKey: ["personnelServices"] });
      
      // If we have specific employee info, invalidate that specific query
      if (response.data && response.data.employeeNumber && response.data.fiscalYear) {
        queryClient.invalidateQueries({ 
          queryKey: ["personnelServices", response.data.employeeNumber, response.data.fiscalYear] 
        });
      }
      
      // Close the dialog
      handleClose();
    },
    onError: (error) => {
      console.error("Delete error:", error);
      toast.error(error.response?.data?.message || "Failed to delete meal allowance");
    }
  });

  const onSubmit = (data) => {
    if (isEditing) {
      setFormData(data);
      setConfirmOpen(true);
    } else {
      mutation.mutate(data);
    }
  };

  const handleConfirmUpdate = () => {
    if (formData) {
      mutation.mutate(formData);
    }
    setConfirmOpen(false);
  };

  const handleOpen = () => setOpen(true);
  const handleClose = () => {
    reset();
    setOpen(false);
  };

  const handleConfirmClose = () => {
    setConfirmOpen(false);
    setFormData(null);
  };

  const employeeOptions = useMemo(() =>
    employees.map((emp) => ({
      ...emp,
      uniqueKey: emp._id || `emp-${Math.random().toString(36).substr(2, 9)}`,
    })), [employees]);

  return (
    <>
      {!row ? (
        <CustomButton onClick={handleOpen} disabled={loading || !settings || settingsLoading || error}>
          {loading || settingsLoading ? <CircularProgress size={24} /> : "Add Meal Allowance"}
        </CustomButton>
      ) : (
        <MenuItem onClick={handleOpen}>Edit</MenuItem>
      )}

      <Dialog 
        open={open} 
        onClose={handleClose} 
        maxWidth="md" 
        fullWidth
        aria-labelledby="meal-allowance-dialog-title"
      >
        <DialogTitle id="meal-allowance-dialog-title">
          {isEditing ? "Edit Meal Allowance" : "Add Meal Allowance"}
        </DialogTitle>
        <DialogContent dividers>
          {loading || settingsLoading ? (
            <Box display="flex" justifyContent="center" p={4}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Typography color="error" align="center" p={2}>
              {error}
            </Typography>
          ) : !settings ? (
            <Typography color="error" align="center" p={2}>
              Unable to load settings. Please try again later.
            </Typography>
          ) : (
            <Grid container spacing={2} sx={{ pt: 1 }}>
              <Grid item xs={12}>
                <Controller
                  name="employee"
                  control={control}
                  rules={{ required: "Employee is required" }}
                  render={({ field }) => (
                    <Autocomplete
                      options={employeeOptions}
                      getOptionLabel={(opt) => opt.employeeFullName || ""}
                      isOptionEqualToValue={(opt, val) => opt._id === val._id}
                      value={field.value}
                      onChange={(_, val) => field.onChange(val)}
                      disabled={isEditing}
                      readOnly={isEditing}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Select Employee"
                          fullWidth
                          error={!!errors.employee}
                          helperText={errors.employee?.message || (!selectedEmployee && "Select an employee to auto-fill details")}
                          InputLabelProps={{ shrink: true }}
                        />
                      )}
                      renderOption={(props, option) => (
                        <li {...props} key={option.uniqueKey}>
                          {option.employeeFullName}
                        </li>
                      )}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={6}>
                <Controller
                  name="actualDays"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Actual Days"
                      type="number"
                      fullWidth
                      disabled={true} // Disable the field
                      InputLabelProps={{ shrink: true }}
                      helperText="Fixed at 22 days per month"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={6}>
                <TextField
                  label="Amount (Annual)"
                  value={Number(computeAmount).toLocaleString("en-PH", {
                    style: "currency",
                    currency: "PHP",
                  })}
                  fullWidth
                  disabled
                  InputLabelProps={{ shrink: true }}
                  helperText={`Annual: ${actualDays} days × ${settings?.meal || 0} PHP/day × 12 months = ${Number(computeAmount).toFixed(2)} PHP/year`}
                />
              </Grid>

              <Grid item xs={6}>
                <Controller
                  name="positionTitle"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Position Title"
                      fullWidth
                      disabled
                      InputLabelProps={{ shrink: true }}
                      helperText={selectedEmployee && !field.value ? "No position title available" : ""}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={6}>
                <Controller
                  name="department"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Department"
                      fullWidth
                      disabled
                      InputLabelProps={{ shrink: true }}
                      helperText={selectedEmployee && !field.value ? "No department available" : ""}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={6}>
                <Controller
                  name="division"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Division"
                      fullWidth
                      disabled
                      InputLabelProps={{ shrink: true }}
                      helperText={selectedEmployee && !field.value ? "No division available" : ""}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={6}>
                <Controller
                  name="region"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Region"
                      fullWidth
                      disabled
                      InputLabelProps={{ shrink: true }}
                      helperText={selectedEmployee && !field.value ? "No region available" : ""}
                    />
                  )}
                />
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={handleClose} 
            disabled={loading || mutation.isLoading || settingsLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit(onSubmit)}
            variant="contained"
            disabled={loading || mutation.isLoading || !settings || settingsLoading || error}
          >
            {isEditing ? "Update" : "Save"}
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={confirmOpen}
        onClose={handleConfirmClose}
        maxWidth="md"
        fullWidth
        aria-labelledby="confirm-update-dialog-title"
      >
        <DialogTitle id="confirm-update-dialog-title">
          Confirm Update
        </DialogTitle>
        <DialogContent dividers>
          <Typography>
            Are you sure you want to update this meal allowance record?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleConfirmClose} disabled={mutation.isLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmUpdate}
            variant="contained"
            disabled={mutation.isLoading}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default MealAllowanceDialog;
