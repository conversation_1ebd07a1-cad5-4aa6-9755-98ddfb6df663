import React, { Component } from "react";
import CustomCreateUpdateDialog from "../medicalallowance/MedicalAllowanceDialog";
import CustomMenu from "../../global/components/CustomMenu";
import CustomTable from "../medicalallowance/MedicalAllowanceCustomTable";
import DashboardHeader from "../../global/components/DashboardHeader";
import PropTypes from "prop-types";

// Custom Error Boundary Component
class CustomErrorBoundary extends Component {
  state = {
    hasError: false,
    error: null,
  };

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  render() {
    if (this.state.hasError) {
      return (
        <div role="alert">
          <p>Something went wrong:</p>
          <pre>{this.state.error?.message || "Unknown error"}</pre>
        </div>
      );
    }
    return this.props.children;
  }
}

const CustomPage = ({
  dataListName,
  schema,
  title = "",
  description = "",
  searchable = true,
  hasEdit = true,
  hasDelete = true,
  hasAdd = true,
  customAddElement = null,
  customEditElement = null,
  additionalMenuOptions = [],
  ROWS_PER_PAGE = 20,
}) => {
  const pageTitle = title || "Medical Allowance";
  const pageDescription = description || `Manage ${dataListName}`;
  const apiPath = `/${dataListName}`;

  const columns = [
    // Add action column first
    {
      field: "action",
      label: "Actions",
      type: "action",
      render: (row) => (
        <CustomMenu
          additionalMenuOptions={additionalMenuOptions}
          customEditElement={customEditElement}
          hasEdit={hasEdit}
          hasDelete={hasDelete}
          row={row}
          schema={schema}
          endpoint={apiPath}
          dataListName={dataListName}
        />
      ),
    },
    // Then add all other columns except action
    ...Object.keys(schema)
      .filter((key) => schema[key].show === true && key !== "action")
      .map((key) => {
        const fieldSchema = schema[key];
        return {
          field: key,
          label: fieldSchema.label,
          type: fieldSchema.type,
          searchable: fieldSchema.searchable || false,
          render: fieldSchema.customRender
            ? (row) => fieldSchema.customRender(row)
            : undefined,
        };
      }),
  ];

  return (
    <CustomErrorBoundary>
      <DashboardHeader
        title={pageTitle}
        description={pageDescription}
        searchable={searchable}
        childElement={
          hasAdd && customAddElement ? (
            customAddElement
          ) : (
            <CustomCreateUpdateDialog
              endpoint={apiPath}
              schema={schema}
              dataListName={dataListName}
            />
          )
        }
      />
      <CustomTable
        dataListName={dataListName}
        apiPath={apiPath}
        ROWS_PER_PAGE={ROWS_PER_PAGE}
        columns={columns}
      />
    </CustomErrorBoundary>
  );
};

CustomPage.propTypes = {
  dataListName: PropTypes.string.isRequired,
  schema: PropTypes.objectOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      type: PropTypes.string.isRequired,
      show: PropTypes.bool,
      searchable: PropTypes.bool,
      customRender: PropTypes.func,
      default: PropTypes.any,
    })
  ).isRequired,
  title: PropTypes.string,
  description: PropTypes.string,
  searchable: PropTypes.bool,
  hasEdit: PropTypes.bool,
  hasDelete: PropTypes.bool,
  hasAdd: PropTypes.bool,
  customAddElement: PropTypes.element,
  customEditElement: PropTypes.element,
  additionalMenuOptions: PropTypes.arrayOf(PropTypes.elementType),
  ROWS_PER_PAGE: PropTypes.number,
};

export default CustomPage;
