import React, { useState, useEffect, useMemo } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  TextField,
  Autocomplete,
  MenuItem,
  CircularProgress,
  Typography,
  Box,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import CustomButton from "../../global/components/CustomButton";
import api from "../../config/api";
import { useUser } from "../../context/UserContext";

const validationSchema = yup.object({
  employee: yup
    .object()
    .nullable()
    .required("Please select an employee"),
  noOfDependents: yup
    .number()
    .required("Number of dependents is required")
    .min(0, "Number of dependents must be at least 0")
    .max(4, "Number of dependents cannot exceed 4")
    .integer("Number of dependents must be a whole number"),
});

const MedicalAllowanceDialog = ({ row, schema, endpoint, dataListName }) => {
  const isEditing = Boolean(row);
  const [open, setOpen] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [formData, setFormData] = useState(null);
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [duplicateCheck, setDuplicateCheck] = useState(null);
  const [checkingDuplicate, setCheckingDuplicate] = useState(false);
  const [personnelService, setPersonnelService] = useState(null);
  const { currentUser } = useUser();
  const queryClient = useQueryClient();
  const [employees, setEmployees] = useState([]);
  // Add state for tracking children allowance record
  const [childrenAllowanceRecord, setChildrenAllowanceRecord] = useState(null);
  const [dependentsMismatch, setDependentsMismatch] = useState(false);

  const { control, handleSubmit, reset, watch, setValue, formState: { errors } } = useForm({
    defaultValues: {
      employee: null,
      noOfDependents: isEditing ? row?.noOfDependents : 0,
    },
    resolver: yupResolver(validationSchema),
  });

  const noOfDependents = watch("noOfDependents");
  const selectedEmployee = watch("employee");

  // Fetch settings with polling to detect changes
  const { data: settingsData, isLoading: settingsLoading, error: settingsError } = useQuery({
    queryKey: ["activeSettings"],
    queryFn: async () => {
      const res = await api.get("/settings/active");
      return res.data;
    },
    refetchInterval: 60000,
    enabled: open,
    onError: (err) => {
      toast.error("Failed to load settings: " + (err.message || "Unknown error"));
      setError("Failed to load settings");
    },
  });

  useEffect(() => {
    if (settingsData) {
      console.log("Settings fetched:", settingsData);
      setSettings(settingsData);
      setError(null);
    }
  }, [settingsData]);

  // Fetch eligible employees (hired before June 1988)
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        const res = await api.get("/getpersonnels/hiredBeforeJune1988");
        setEmployees(res.data);
      } catch (err) {
        const errorMsg = err.message || "Failed to fetch employees";
        setError(errorMsg);
        toast.error(errorMsg);
        setEmployees([]);
      } finally {
        setLoading(false);
      }
    };

    if (open) {
      fetchData();
    }
  }, [open]);

  // Fetch PersonnelServices and fallback to ChildrenAllowance for noOfDependent
  useEffect(() => {
    const fetchPersonnelService = async () => {
      if (!selectedEmployee || !settings?.fiscalYear) return;

      try {
        // First, try to get noOfDependent from PersonnelServices
        const personnelRes = await api.get("/personnel-services", {
          params: {
            employeeNumber: selectedEmployee.employeeNumber,
            fiscalYear: settings.fiscalYear,
          },
        });

        if (personnelRes.data.personnelServices && personnelRes.data.personnelServices.length > 0) {
          setPersonnelService(personnelRes.data.personnelServices[0]);
          const defaultDependents = personnelRes.data.personnelServices[0].noOfDependent || 0;
          if (defaultDependents > 0) {
            setValue("noOfDependents", defaultDependents, { shouldValidate: true });
            return;
          }
        } else {
          setPersonnelService(null);
        }

        // If no PersonnelServices record or noOfDependent is 0, fallback to ChildrenAllowance
        const childrenAllowanceRes = await api.get("/children-allowance", {
          params: {
            employeeNumber: selectedEmployee.employeeNumber,
            fiscalYear: settings.fiscalYear,
            budgetType: settings.budgetType,
          },
        });

        if (childrenAllowanceRes.data.data && childrenAllowanceRes.data.data.length > 0) {
          const defaultDependents = childrenAllowanceRes.data.data[0].noOfDependents || 0;
          setValue("noOfDependents", defaultDependents, { shouldValidate: true });
        } else {
          setValue("noOfDependents", 0, { shouldValidate: true });
        }
      } catch (err) {
        console.error("Error fetching PersonnelServices or ChildrenAllowance:", err);
        setPersonnelService(null);
        setValue("noOfDependents", 0, { shouldValidate: true });
      }
    };

    fetchPersonnelService();
  }, [selectedEmployee, settings, setValue]);

  // Check for existing MedicalAllowance record when employee is selected
  useEffect(() => {
    const checkDuplicate = async () => {
      if (!selectedEmployee || isEditing || !settings?.fiscalYear || !settings?.budgetType) return;

      setCheckingDuplicate(true);
      try {
        const res = await api.get("/medical-allowance", {
          params: {
            search: selectedEmployee.employeeNumber,
            fiscalYear: settings.fiscalYear,
            budgetType: settings.budgetType,
            limit: 100
          },
        });
        
        // Check if any of the returned records match this employee exactly
        const existingRecord = res.data.data.find(record => 
          record.employeeNumber === selectedEmployee.employeeNumber &&
          record.fiscalYear === settings.fiscalYear &&
          record.budgetType === settings.budgetType
        );
        
        if (existingRecord) {
          setDuplicateCheck(`A medical allowance record already exists for ${selectedEmployee.employeeFullName} in ${settings.fiscalYear} (${settings.budgetType})`);
        } else {
          setDuplicateCheck(null);
        }
      } catch (err) {
        console.error("Error checking for duplicate:", err);
        setDuplicateCheck("Unable to check for existing records");
      } finally {
        setCheckingDuplicate(false);
      }
    };

    checkDuplicate();
  }, [selectedEmployee, settings, isEditing]);

  // Check for existing Children Allowance record when employee is selected
  useEffect(() => {
    const checkChildrenAllowance = async () => {
      if (!selectedEmployee || !settings?.fiscalYear || isEditing) return;
      
      try {
        const res = await api.get("/children-allowance", {
          params: {
            employeeNumber: selectedEmployee.employeeNumber,
            fiscalYear: settings.fiscalYear,
            budgetType: settings.budgetType,
          },
        });
        
        if (res.data.data && res.data.data.length > 0) {
          const childrenRecord = res.data.data[0];
          setChildrenAllowanceRecord(childrenRecord);
          
          // Set the number of dependents to match children allowance
          setValue("noOfDependents", childrenRecord.noOfDependents, { shouldValidate: true });
        } else {
          setChildrenAllowanceRecord(null);
        }
      } catch (err) {
        console.error("Error checking children allowance:", err);
        setChildrenAllowanceRecord(null);
      }
    };
    
    checkChildrenAllowance();
  }, [selectedEmployee, settings, isEditing, setValue]);

  // Check for mismatch when noOfDependents changes
  useEffect(() => {
    if (childrenAllowanceRecord && noOfDependents !== childrenAllowanceRecord.noOfDependents) {
      setDependentsMismatch(true);
    } else {
      setDependentsMismatch(false);
    }
  }, [noOfDependents, childrenAllowanceRecord]);

  useEffect(() => {
    if (isEditing && row) {
      reset({
        employee: {
          _id: row.employeeId,
          employeeNumber: row.employeeNumber,
          employeeFullName: row.employeeFullName,
          positionTitle: row.positionTitle,
          department: row.department,
          division: row.division,
          region: row.region,
        },
        noOfDependents: row.noOfDependents || 0,
      });
    }
  }, [isEditing, row, reset]);

  useEffect(() => {
    if (selectedEmployee && !isEditing && !personnelService) {
      setValue("noOfDependents", 0, { shouldValidate: true });
    }
  }, [selectedEmployee, personnelService, setValue, isEditing]);

  const computeAmount = useMemo(() => {
    if (!settings || !settings.medicalAllowance || noOfDependents === undefined) return 0;
    return (Number(settings.medicalAllowance) * Number(noOfDependents) * 12).toFixed(2);
  }, [settings, noOfDependents]);

  const mutation = useMutation({
    mutationFn: async (data) => {
      if (!data.employee) throw new Error("Employee is required");
      if (!data.employee.employeeNumber) throw new Error("Employee number is missing");
      if (!Number.isInteger(Number(data.noOfDependents))) throw new Error("Number of dependents must be an integer");
      if (!settings?.fiscalYear || !settings?.budgetType) throw new Error("Fiscal year and budget type are required");

      const payload = {
        employeeId: data.employee._id,
        employeeNumber: data.employee.employeeNumber,
        employeeFullName: data.employee.employeeFullName,
        positionTitle: data.employee.positionTitle,
        department: data.employee.department,
        division: data.employee.division,
        region: data.employee.region,
        noOfDependents: Number(data.noOfDependents),
        amount: Number(computeAmount),
        processBy: `${currentUser.FirstName} ${currentUser.LastName}`,
        processDate: new Date().toISOString(),
        fiscalYear: settings.fiscalYear,
        budgetType: settings.budgetType,
      };

      console.log("Sending payload:", payload);

      return isEditing
        ? await api.put(`${endpoint}/${row._id}`, payload)
        : await api.post(endpoint, payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [dataListName] });
      queryClient.invalidateQueries({ queryKey: ["personnelServices"] });
      toast.success(
        isEditing
          ? "Medical allowance updated successfully"
          : "Medical allowance created successfully"
      );
      handleClose();
    },
    onError: (err) => {
      console.error("Mutation error:", err);
      const errorMessage = err.response?.data?.error || err.response?.data?.message || err.message || "Failed to save medical allowance";
      toast.error(errorMessage);
    },
  });

  const onSubmit = (data) => {
    if (isEditing) {
      setFormData(data);
      setConfirmOpen(true);
    } else {
      mutation.mutate(data);
    }
  };

  const handleConfirmUpdate = () => {
    if (formData) {
      mutation.mutate(formData);
    }
    setConfirmOpen(false);
  };

  const handleOpen = () => setOpen(true);
  const handleClose = () => {
    reset();
    setOpen(false);
    setEmployees([]);
    setPersonnelService(null);
    setError(null);
    setDuplicateCheck(null);
    setCheckingDuplicate(false);
  };

  const handleConfirmClose = () => {
    setConfirmOpen(false);
    setFormData(null);
  };

  const employeeOptions = useMemo(() =>
    Array.isArray(employees)
      ? employees.map((emp) => ({
          ...emp,
          uniqueKey: emp._id || `emp-${Math.random().toString(36).substr(2, 9)}`,
        }))
      : [], [employees]);

  return (
    <>
      {!row ? (
        <CustomButton
          onClick={handleOpen}
          disabled={loading || !settings || settingsLoading || !!error}
        >
          {loading || settingsLoading ? <CircularProgress size={24} /> : "Add Medical Allowance"}
        </CustomButton>
      ) : (
        <MenuItem onClick={handleOpen}>Edit</MenuItem>
      )}

      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="md"
        fullWidth
        aria-labelledby="medical-allowance-dialog-title"
      >
        <DialogTitle id="medical-allowance-dialog-title">
          {isEditing ? "Edit Medical Allowance" : "Add Medical Allowance"}
        </DialogTitle>
        <DialogContent dividers>
          {loading || settingsLoading ? (
            <Box display="flex" justifyContent="center" p={4}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Typography color="error" align="center" p={2}>
              {error}
            </Typography>
          ) : !settings ? (
            <Typography color="error" align="center" p={2}>
              Unable to load settings. Please try again later.
            </Typography>
          ) : (
            <Grid container spacing={2} sx={{ pt: 1 }}>
              {duplicateCheck && !isEditing && (
                <Grid item xs={12}>
                  <Typography color="error" align="center">
                    {duplicateCheck}
                  </Typography>
                </Grid>
              )}
              <Grid item xs={12}>
                <Controller
                  name="employee"
                  control={control}
                  render={({ field }) => (
                    <Autocomplete
                      options={employeeOptions}
                      getOptionLabel={(opt) => opt.employeeFullName || ""}
                      isOptionEqualToValue={(opt, val) => opt._id === val._id}
                      value={field.value}
                      onChange={(_, val) => field.onChange(val)}
                      disabled={isEditing}
                      readOnly={isEditing}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Select Employee (Hired before June 1988)"
                          fullWidth
                          error={!!errors.employee}
                          helperText={errors.employee?.message || (!selectedEmployee && "Select an employee to auto-fill details")}
                          InputLabelProps={{ shrink: true }}
                        />
                      )}
                      renderOption={(props, option) => (
                        <li {...props} key={option.uniqueKey}>
                          {option.employeeFullName}
                        </li>
                      )}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={6}>
                <Controller
                  name="noOfDependents"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Number of Dependents"
                      type="number"
                      fullWidth
                      InputProps={{
                        inputProps: { 
                          min: 0, 
                          max: 4,
                          step: 1 
                        },
                      }}
                      onChange={(e) => {
                        const value = parseInt(e.target.value);
                        const validValue = isNaN(value) ? 0 : Math.min(Math.max(value, 0), 4);
                        field.onChange(validValue);
                        
                        // Recalculate amount when noOfDependents changes
                        if (settings?.medicalAllowance) {
                          const amount = validValue * settings.medicalAllowance;
                          setValue("amount", amount);
                        }
                      }}
                      error={!!errors.noOfDependents}
                      helperText={errors.noOfDependents?.message || "Maximum of 4 dependents allowed"}
                      InputLabelProps={{ shrink: true }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={6}>
                <TextField
                  label="Amount"
                  value={Number(computeAmount).toLocaleString("en-PH", {
                    style: "currency",
                    currency: "PHP",
                  })}
                  fullWidth
                  disabled
                  InputLabelProps={{ shrink: true }}
                  helperText={`Amount: ${noOfDependents} dependents × ${settings?.medicalAllowance || 0} PHP/month × 12 months = ${Number(computeAmount).toFixed(2)} PHP/year`}
                />
              </Grid>

              {selectedEmployee && (
                <>
                  <Grid item xs={6}>
                    <TextField
                      label="Employee Number"
                      value={selectedEmployee.employeeNumber || ""}
                      fullWidth
                      disabled
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      label="Employee Name"
                      value={selectedEmployee.employeeFullName || ""}
                      fullWidth
                      disabled
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      label="Position Title"
                      value={selectedEmployee.positionTitle || ""}
                      fullWidth
                      disabled
                      InputLabelProps={{ shrink: true }}
                      helperText={selectedEmployee && !selectedEmployee.positionTitle ? "No position title available" : ""}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      label="Department"
                      value={selectedEmployee.department || ""}
                      fullWidth
                      disabled
                      InputLabelProps={{ shrink: true }}
                      helperText={selectedEmployee && !selectedEmployee.department ? "No department available" : ""}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      label="Division"
                      value={selectedEmployee.division || ""}
                      fullWidth
                      disabled
                      InputLabelProps={{ shrink: true }}
                      helperText={selectedEmployee && !selectedEmployee.division ? "No division available" : ""}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      label="Region"
                      value={selectedEmployee.region || ""}
                      fullWidth
                      disabled
                      InputLabelProps={{ shrink: true }}
                      helperText={selectedEmployee && !selectedEmployee.region ? "No region available" : ""}
                    />
                  </Grid>
                </>
              )}
              {childrenAllowanceRecord && dependentsMismatch && (
                <Grid item xs={12}>
                  <Typography color="error" variant="body2">
                    Warning: Number of dependents does not match Children Allowance record ({childrenAllowanceRecord.noOfDependents}). 
                    Both records should have the same number of dependents.
                  </Typography>
                </Grid>
              )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleClose}
            disabled={loading || mutation.isLoading || settingsLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit(onSubmit)}
            variant="contained"
            disabled={loading || mutation.isLoading || !settings || settingsLoading || !!error || !!duplicateCheck || checkingDuplicate || dependentsMismatch}
          >
            {isEditing ? "Update" : "Save"}
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={confirmOpen}
        onClose={handleConfirmClose}
        maxWidth="md"
        fullWidth
        aria-labelledby="confirm-update-dialog-title"
      >
        <DialogTitle id="confirm-update-dialog-title">
          Confirm Update
        </DialogTitle>
        <DialogContent dividers>
          <Typography>
            Are you sure you want to update this medical allowance record?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleConfirmClose} disabled={mutation.isLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmUpdate}
            variant="contained"
            disabled={mutation.isLoading}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default MedicalAllowanceDialog;
