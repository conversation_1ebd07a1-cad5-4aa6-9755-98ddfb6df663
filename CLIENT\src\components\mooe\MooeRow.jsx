import React, { memo } from "react";
import {
  TableRow,
  TableCell,
  IconButton,
  TextField,
  InputAdornment,
  Typography,
  Button,
} from "@mui/material";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import RemoveCircleOutlineIcon from "@mui/icons-material/RemoveCircleOutline";
import AddIcon from "@mui/icons-material/Add";
import { NumericFormat } from "react-number-format";

// Custom component for numeric input formatting
const NumberFormatCustom = React.forwardRef(function NumberFormatCustom(props, ref) {
  const { onChange, ...other } = props;
  return (
    <NumericFormat
      {...other}
      getInputRef={ref}
      thousandSeparator
      decimalScale={2}
      fixedDecimalScale
      onValueChange={(values) => {
        onChange({
          target: {
            value: values.value,
          },
        });
      }}
    />
  );
});

const MooeRow = memo(({
  row,
  expanded,
  onToggleExpand,
  onAmountChange,
  onTitleChange,
  onAddCustomMOOE,
  calculateTotal,
  status,
  onIncomeChange,
  onSubsidyChange,
  disableIncomeInputs = false, // Add this prop with default value
}) => {
  const isEditable = ["Not Submitted", "Returned", "Draft"].includes(status);
  
  // Calculate totals for this row
  const totalIncome = row.children.reduce((sum, child) => {
    return sum + parseFloat(child.income || 0);
  }, 0);
  
  const totalSubsidy = row.children.reduce((sum, child) => {
    return sum + parseFloat(child.subsidy || 0);
  }, 0);
  
  const totalAmount = totalIncome + totalSubsidy;

  // Check if this row contains the "Other MOOE" UACS code
  const hasOtherMOOE = row.children.some(child => child.uacsCode === "5-02-99-990");

  return (
    <>
      {/* Parent row */}
      <TableRow 
        sx={{ 
          backgroundColor: expanded ? "rgba(55, 94, 56, 0.08)" : "rgba(55, 94, 56, 0.04)",
          '&:hover': { backgroundColor: "rgba(55, 94, 56, 0.12)" }
        }}
      >
        <TableCell>
          <IconButton
            size="small"
            onClick={() => onToggleExpand(row.id)}
            sx={{ color: "#375e38" }}
          >
            {expanded ? <RemoveCircleOutlineIcon /> : <AddCircleOutlineIcon />}
          </IconButton>
        </TableCell>
        <TableCell sx={{ fontWeight: "bold" }}>{row.sublineItem}</TableCell>
        <TableCell colSpan={2}></TableCell>
        <TableCell align="right" sx={{ fontWeight: "bold" }}>
          ₱{totalIncome.toLocaleString(undefined, { minimumFractionDigits: 2 })}
        </TableCell>
        <TableCell align="right" sx={{ fontWeight: "bold" }}>
          ₱{totalSubsidy.toLocaleString(undefined, { minimumFractionDigits: 2 })}
        </TableCell>
        <TableCell align="right" sx={{ fontWeight: "bold" }}>
          ₱{totalAmount.toLocaleString(undefined, { minimumFractionDigits: 2 })}
        </TableCell>
      </TableRow>

      {/* Child rows */}
      {expanded && row.children.map((child) => (
        <TableRow key={child.id}>
          <TableCell></TableCell>
          <TableCell></TableCell>
          <TableCell>
            {child.custom ? (
              <TextField
                fullWidth
                size="small"
                value={child.accountingTitle || ""}
                onChange={(e) => onTitleChange(row.id, child.id, e.target.value)}
                disabled={!isEditable}
                placeholder="Enter MOOE title"
                sx={{ my: 0.5 }}
              />
            ) : (
              child.accountingTitle
            )}
          </TableCell>
          <TableCell>{child.uacsCode}</TableCell>
          
          {/* Income field with NumberFormatCustom */}
          <TableCell>
            <TextField
              fullWidth
              size="small"
              value={child.income || "0"}
              onChange={(e) => onIncomeChange(row.id, child.id, e.target.value)}
              disabled={!isEditable || disableIncomeInputs} // Add disableIncomeInputs here
              InputProps={{
                startAdornment: <InputAdornment position="start">₱</InputAdornment>,
                inputComponent: NumberFormatCustom,
                inputProps: { style: { textAlign: 'right' } }
              }}
              sx={{ 
                backgroundColor: !isEditable || disableIncomeInputs ? "#f5f5f5" : "white",
                '& .MuiInputBase-input': {
                  textAlign: 'right'
                }
              }}
            />
          </TableCell>
          
          {/* Subsidy field with NumberFormatCustom */}
          <TableCell>
            <TextField
              fullWidth
              size="small"
              value={child.subsidy || "0"}
              onChange={(e) => onSubsidyChange(row.id, child.id, e.target.value)}
              disabled={!isEditable}
              InputProps={{
                startAdornment: <InputAdornment position="start">₱</InputAdornment>,
                inputComponent: NumberFormatCustom,
                inputProps: { style: { textAlign: 'right' } }
              }}
            />
          </TableCell>
          
          {/* Amount field (calculated) */}
          <TableCell>
            <Typography variant="body2" align="right">
              ₱{(parseFloat(child.income || 0) + parseFloat(child.subsidy || 0)).toLocaleString(undefined, { minimumFractionDigits: 2 })}
            </Typography>
          </TableCell>
        </TableRow>
      ))}

      {/* Add Custom MOOE button row - only show if expanded and has Other MOOE */}
      {expanded && hasOtherMOOE && isEditable && (
        <TableRow>
          <TableCell></TableCell>
          <TableCell></TableCell>
          <TableCell colSpan={5}>
            <Button
              variant="outlined"
              size="small"
              startIcon={<AddIcon />}
              onClick={() => onAddCustomMOOE(row.id)}
              sx={{
                my: 1,
                color: "#375e38",
                borderColor: "#375e38",
                '&:hover': {
                  backgroundColor: "rgba(55, 94, 56, 0.08)",
                  borderColor: "#375e38",
                }
              }}
            >
              Add Other MOOE
            </Button>
          </TableCell>
        </TableRow>
      )}
    </>
  );
});

export default MooeRow;
