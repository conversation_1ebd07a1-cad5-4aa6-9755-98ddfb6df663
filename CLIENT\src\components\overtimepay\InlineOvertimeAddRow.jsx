import React, { useState, useEffect, useCallback } from "react";
import {
  TableRow,
  TableCell,
  TextField,
  IconButton,
  Autocomplete,
} from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import api from "../../config/api";
import { toast } from "react-hot-toast";
import SaveIcon from "@mui/icons-material/Save";
import { useUser } from "../../context/UserContext";

const InlineOvertimeAddRow = ({ refreshData, employeeOptions }) => {
  const { currentUser } = useUser();
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [weekdayHours, setWeekdayHours] = useState("");
  const [weekendHours, setWeekendHours] = useState("");
  const [amount, setAmount] = useState(0);
  const [settings, setSettings] = useState(null);
  const [multipliers, setMultipliers] = useState({
    weekdayMultiplier: 1.25,
    weekendMultiplier: 1.5,
  });

  // NEW: track which employees already have records
  const [existingEmployees, setExistingEmployees] = useState([]);
  // NEW: final dropdown options after removing duplicates & final-status personnel
  const [filteredEmployeeOptions, setFilteredEmployeeOptions] = useState([]);

  const processBy = `${currentUser.FirstName} ${currentUser.LastName}`;
  const fiscalYear = settings?.fiscalYear || "";
  const budgetType = settings?.budgetType || "";

  // fetch active settings
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const res = await api.get("/settings/active");
        if (res.data) {
          setSettings(res.data);
          setMultipliers({
            weekdayMultiplier: res.data.weekdayMultiplier || 1.25,
            weekendMultiplier: res.data.weekendMultiplier || 1.5,
          });
        } else {
          toast.error("No active settings found.");
        }
      } catch (err) {
        console.error("Error fetching settings:", err);
        toast.error("Failed to fetch settings.");
      }
    };
    fetchSettings();
  }, []);

  // fetch existing overtime records for this year
  const fetchExistingRecords = useCallback(async () => {
    if (!fiscalYear) return;
    try {
      const res = await api.get("/overtime-pay", { params: { fiscalYear } });
      setExistingEmployees(res.data.data.map((rec) => rec.employeeFullName));
    } catch (error) {
      console.error("Error fetching overtime records:", error);
    }
  }, [fiscalYear]);

  useEffect(() => {
    fetchExistingRecords();
  }, [fiscalYear, fetchExistingRecords]);

  // NEW: filter out those already saved AND those whose status is Submitted/Approved
  useEffect(() => {
    setFilteredEmployeeOptions(
      employeeOptions.filter((option) =>
        !existingEmployees.includes(option.employeeFullName) &&
        !["Submitted", "Approved"].includes(option.status)
      )
    );
  }, [employeeOptions, existingEmployees]);

  // compute amount
  const computeAmount = useCallback(() => {
    if (!selectedEmployee || !selectedEmployee.monthlySalary) return 0;
    const monthlySalary = Number(selectedEmployee.monthlySalary) || 0;
    const weekdayRate = (monthlySalary / 22 / 8) * multipliers.weekdayMultiplier;
    const weekendRate = (monthlySalary / 22 / 8) * multipliers.weekendMultiplier;
    const computed =
      Math.max(0, Number(weekdayHours)) * weekdayRate +
      Math.max(0, Number(weekendHours)) * weekendRate;
    return isNaN(computed) ? 0 : computed;
  }, [selectedEmployee, weekdayHours, weekendHours, multipliers]);

  useEffect(() => {
    setAmount(computeAmount());
  }, [computeAmount]);

  const mutation = useMutation({
    mutationFn: async (data) => await api.post("/overtime-pay", data),
    onSuccess: () => {
      toast.success("Overtime record added successfully");
      setSelectedEmployee(null);
      setWeekdayHours("");
      setWeekendHours("");
      refreshData();
      fetchExistingRecords();
    },
    onError: (err) => {
      toast.error(err.response?.data?.message || "Error adding record");
    },
  });

  const handleSave = () => {
    if (!selectedEmployee) {
      toast.error("Please select an employee");
      return;
    }
    if (!settings) {
      toast.error("Settings not loaded. Please refresh.");
      return;
    }
    const payload = {
      employeeFullName: selectedEmployee.employeeFullName,
      positionTitle: selectedEmployee.positionTitle,
      weekdayHours: Number(weekdayHours),
      weekendHours: Number(weekendHours),
      monthlySalary: Number(selectedEmployee.monthlySalary) || 0,
      processBy,
      fiscalYear,
      budgetType,
      processDate: new Date(),
    };
    mutation.mutate(payload);
  };

  return (
    <TableRow>
      <TableCell>
        <Autocomplete
          options={filteredEmployeeOptions}
          getOptionLabel={(option) => option.employeeFullName || ""}
          isOptionEqualToValue={(option, value) => option._id === value._id}
          value={selectedEmployee}
          onChange={(e, newValue) => setSelectedEmployee(newValue)}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder="Select Employee"
              variant="standard"
              sx={{ minWidth: "auto" }}
            />
          )}
        />
      </TableCell>
      <TableCell>
        <TextField
          variant="standard"
          type="number"
          value={weekdayHours}
          onChange={(e) => setWeekdayHours(e.target.value)}
          placeholder="Weekday Hrs"
        />
      </TableCell>
      <TableCell>
        <TextField
          variant="standard"
          type="number"
          value={weekendHours}
          onChange={(e) => setWeekendHours(e.target.value)}
          placeholder="Weekend Hrs"
        />
      </TableCell>
      <TableCell>
        <TextField
          variant="standard"
          value={amount.toLocaleString("en-PH", {
            style: "currency",
            currency: "PHP",
          })}
          disabled
        />
      </TableCell>
      <TableCell>
        <IconButton onClick={handleSave} color="primary">
          <SaveIcon />
        </IconButton>
      </TableCell>
    </TableRow>
  );
};

export default InlineOvertimeAddRow;
