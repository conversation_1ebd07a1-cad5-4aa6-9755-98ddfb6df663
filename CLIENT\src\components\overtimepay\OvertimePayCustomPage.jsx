import React from "react";
import CustomTable from "../overtimepay/OvertimePayCustomTable";
import DashboardHeader from "../../global/components/DashboardHeader";
import PropTypes from "prop-types";

const CustomPage = ({
  dataListName,
  schema,
  title = "",
  description = "",
  searchable = true,
  hasEdit = true,
  hasDelete = true,
  hasAdd = true,
  customAddElement = null,
  customEditElement = null,
  additionalMenuOptions = [],
  ROWS_PER_PAGE = 20,
  debug = false,
}) => {
  const pageTitle =
    title || dataListName.charAt(0).toUpperCase() + dataListName.slice(1);
  const pageDescription = description || `Manage ${dataListName}`;
  const apiPath = `/${dataListName}`;

  return (
    <>
      <DashboardHeader
        title={pageTitle}
        description={pageDescription}
        searchable={searchable}
        childElement={
          // Kapag hasAdd ay false, walang add button ang ipapakita.
          hasAdd
            ? (customAddElement || (
                <div>
                  {/* Default add component kung gusto mo itong gamitin */}
                </div>
              ))
            : null
        }
      />
      <CustomTable
        dataListName={dataListName}
        apiPath={apiPath}
        ROWS_PER_PAGE={ROWS_PER_PAGE}
        columns={Object.keys(schema)
          .filter((key) => schema[key].show === true || key === "action")
          .map((key) => {
            const fieldSchema = schema[key];
            const column = {
              field: key,
              label: fieldSchema.label,
              type: fieldSchema.type,
              searchable: fieldSchema.searchable || false,
            };

            if (fieldSchema.type === "action") {
              column.render = (row) => (
                <div>
                  {additionalMenuOptions.map((MenuOption, index) => (
                    <div key={index}>
                      <MenuOption
                        row={row}
                        endpoint={apiPath}
                        dataListName={dataListName}
                      />
                    </div>
                  ))}
                </div>
              );
            }
            if (fieldSchema.customRender) {
              column.render = (row) => fieldSchema.customRender(row);
            }
            return column;
          })}
      />
    </>
  );
};

CustomPage.propTypes = {
  dataListName: PropTypes.string.isRequired,
  schema: PropTypes.object.isRequired,
  title: PropTypes.string,
  description: PropTypes.string,
  searchable: PropTypes.bool,
  hasEdit: PropTypes.bool,
  hasDelete: PropTypes.bool,
  hasAdd: PropTypes.bool,
  customAddElement: PropTypes.element,
  customEditElement: PropTypes.element,
  additionalMenuOptions: PropTypes.arrayOf(PropTypes.func),
  ROWS_PER_PAGE: PropTypes.number,
  debug: PropTypes.bool,
};

export default CustomPage;
