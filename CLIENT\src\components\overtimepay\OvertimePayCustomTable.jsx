/************************************************************
 * File: components/overtimepay/OvertimePayCustomTable.jsx
 ************************************************************/
import React, { useEffect, useState, useCallback } from "react";
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TableSortLabel,
  IconButton,
  Tooltip,
  Popover,
  Button,
  Select,
  MenuItem,
  TextField,
} from "@mui/material";
import { grey } from "@mui/material/colors";
import { useQuery } from "@tanstack/react-query";
import dayjs from "dayjs";
import { TiFilter } from "react-icons/ti";
import api from "../../config/api";
import { useSearch } from "../../context/SearchContext";
import TableBodyLoading from "./TableBodyLoading";
import { isValidDate } from "../../utils/formatDate";
import InlineOvertimeAddRow from "./InlineOvertimeAddRow";
import OvertimePayEditableRow from "./OvertimePayEditableRow";

const OvertimePayCustomTable = ({
  columns,
  ROWS_PER_PAGE = 20,
  apiPath = "/overtime-pay",
  dataListName = "overtimePays",
  orderByDefault = "updatedAt",
}) => {
  const { searchValue, setSearchValue } = useSearch();

  const [order, setOrder] = useState("desc");
  const [orderBy, setOrderBy] = useState(orderByDefault);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(ROWS_PER_PAGE);
  const [filterAnchorEl, setFilterAnchorEl] = useState(null);
  const [fieldAndValue, setFieldAndValue] = useState({
    field: "",
    value: "",
    label: "",
    operator: "=",
  });
  const [employeeOptions, setEmployeeOptions] = useState([]);

  const { data, isLoading, refetch } = useQuery({
    queryKey: [dataListName, page, rowsPerPage, searchValue, fieldAndValue, orderBy, order],
    queryFn: async () => {
      const res = await api.get(apiPath, {
        params: {
          page: page + 1,
          limit: rowsPerPage,
          search: searchValue,
          [fieldAndValue.field]: fieldAndValue.value,
          orderBy,
          order,
          operator: fieldAndValue.operator,
        },
      });
      return res.data;
    },
  });

  const fetchEmployees = async () => {
    try {
      const res = await api.get("/getpersonnels");
      let data = res.data || [];
      // De-duplicate employee options
      const uniqueData = [];
      const seen = new Set();
      for (const emp of data) {
        const key = emp.EmployeeID || emp.employeeFullName;
        if (!seen.has(key)) {
          seen.add(key);
          uniqueData.push(emp);
        }
      }
      setEmployeeOptions(uniqueData);
    } catch (err) {
      console.error("Error fetching employees:", err);
    }
  };

  useEffect(() => {
    fetchEmployees();
  }, []);

  const refreshData = () => {
    refetch();
  };

  // Debounce refetch
  useEffect(() => {
    const debouncedSearch = setTimeout(() => refetch(), 500);
    return () => clearTimeout(debouncedSearch);
  }, [order, orderBy, rowsPerPage, refetch]);

  useEffect(() => {
    if (fieldAndValue.field === "date" && fieldAndValue.value) {
      if (isValidDate(fieldAndValue.value)) refetch();
    } else if (searchValue && searchValue.split("-").length === 3) {
      if (isValidDate(searchValue)) refetch();
    } else {
      const debouncedSearch = setTimeout(() => refetch(), 500);
      return () => clearTimeout(debouncedSearch);
    }
  }, [searchValue, fieldAndValue, refetch]);

  useEffect(() => {
    if (searchValue && (fieldAndValue.field || fieldAndValue.label || fieldAndValue.value)) {
      setFieldAndValue({ field: "", label: "", value: "", operator: "=" });
    }
  }, [searchValue]);

  useEffect(() => {
    if (fieldAndValue.value && searchValue) {
      setSearchValue("");
    }
  }, [fieldAndValue.value, searchValue, setSearchValue]);

  const handleRequestSort = useCallback(
    (property) => {
      const isAsc = orderBy === property && order === "asc";
      setOrder(isAsc ? "desc" : "asc");
      setOrderBy(property);
    },
    [order, orderBy]
  );

  const handleFilterClick = (event, columnKey, columnLabel) => {
    setFilterAnchorEl(event.currentTarget);
    if (fieldAndValue.field !== columnKey) {
      setFieldAndValue({ field: columnKey, value: "", label: columnLabel, operator: "=" });
    }
  };

  const handleFilterClearValue = () =>
    setFieldAndValue((prev) => ({ ...prev, value: "" }));

  const handleFilterClose = () => setFilterAnchorEl(null);

  const handleChangePage = (event, newPage) => setPage(newPage);

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleDateChange = (e) => {
    const [year, month, day] = e.target.value.split("-");
    const formattedValue = `${month}-${day}-${year}`;
    setFieldAndValue((prev) => ({ ...prev, value: formattedValue }));
  };

  const getFormattedValue = () => {
    if (!fieldAndValue.value) return "";
    const [month, day, year] = fieldAndValue.value.split("-");
    return `${year}-${month}-${day}`;
  };

  const renderFilter = () => {
    const column = columns.find((col) => col.field === fieldAndValue.field);
    if (!column) return null;

    if (column.type === "date") {
      return (
        <>
          <TextField
            size="small"
            type="date"
            value={getFormattedValue()}
            onChange={handleDateChange}
            fullWidth
          />
          {fieldAndValue.value && (
            <Button onClick={handleFilterClearValue} size="small" color="error">
              Clear
            </Button>
          )}
        </>
      );
    } else if (column.type === "number") {
      return (
        <>
          <Select
            size="small"
            value={fieldAndValue.operator || "="}
            onChange={(e) =>
              setFieldAndValue((prev) => ({ ...prev, operator: e.target.value }))
            }
            fullWidth
          >
            <MenuItem value="=">Equal (=)</MenuItem>
            <MenuItem value="<">Less than (&lt;)</MenuItem>
            <MenuItem value=">">Greater than (&gt;)</MenuItem>
            <MenuItem value="<=">Less than or Equal (≤)</MenuItem>
            <MenuItem value=">=">Greater than or Equal (≥)</MenuItem>
          </Select>
          <TextField
            size="small"
            type="number"
            value={fieldAndValue.value || ""}
            onChange={(e) =>
              setFieldAndValue((prev) => ({ ...prev, value: e.target.value }))
            }
            fullWidth
          />
          {fieldAndValue.value && (
            <Button onClick={handleFilterClearValue} size="small" color="error">
              Clear
            </Button>
          )}
        </>
      );
    } else if (column.type === "boolean") {
      return (
        <>
          <Select
            size="small"
            value={fieldAndValue.value !== undefined ? fieldAndValue.value : ""}
            onChange={(e) =>
              setFieldAndValue((prev) => ({
                ...prev,
                value: e.target.value === "true",
              }))
            }
            fullWidth
          >
            <MenuItem value="">All</MenuItem>
            <MenuItem value="true">Yes</MenuItem>
            <MenuItem value="false">No</MenuItem>
          </Select>
          {fieldAndValue.value !== "" && (
            <Button onClick={handleFilterClearValue} size="small" color="error">
              Clear
            </Button>
          )}
        </>
      );
    }
    return (
      <>
        <TextField
          size="small"
          placeholder={`Search by ${fieldAndValue.label}`}
          value={fieldAndValue.value}
          onChange={(e) =>
            setFieldAndValue((prev) => ({ ...prev, value: e.target.value }))
          }
          fullWidth
        />
        {fieldAndValue.value && (
          <Button onClick={handleFilterClearValue} size="small" color="error">
            Clear
          </Button>
        )}
      </>
    );
  };

  // Function para ibigay ang alignment base sa column index
  const getAlignment = (index) => {
    if (index === 0) return "left";
    if (index === 1 || index === 2) return "center";
    if (index === 3) return "right";
    if (index === 4) return "center";
    return "left";
  };

  const rows = data?.data || [];

  return (
    <Box overflow="auto">
      <Paper sx={{ width: "100%", overflow: "hidden", borderRadius: 0 }}>
        <TableContainer sx={{ height: "78vh" }}>
          <Table size="small">
            {/* HEAD */}
            <TableHead
              sx={{
                "& .MuiTableCell-root": {
                  fontWeight: "bold", // Bold ang header font
                  color: "#fff",
                },
              }}
            >
              <TableRow
                sx={{
                  position: "sticky",
                  top: 0,
                  backgroundColor: "#375e38",
                  zIndex: 2,
                  borderBottom: "2px solid #fff", // Horizontal grid line
                }}
              >
                {columns.map((column, index) => (
                  <TableCell
                    key={column.field}
                    sx={{
                      borderRight: "1px solid",
                      borderColor: grey[500],
                      textAlign: getAlignment(index),
                    }}
                  >
                    <Box display="flex" alignItems="center" justifyContent="space-between">
                      <TableSortLabel
                        active={orderBy === column.field}
                        direction={orderBy === column.field ? order : "asc"}
                        onClick={() => handleRequestSort(column.field)}
                      >
                        {column.label}
                      </TableSortLabel>
                      {column.type !== "action" && (
                        <Tooltip title={`Filter ${column.label}`} arrow>
                          <IconButton
                            size="small"
                            onClick={(event) =>
                              handleFilterClick(event, column.field, column.label)
                            }
                          >
                            <TiFilter color="lightgray" />
                          </IconButton>
                        </Tooltip>
                      )}
                    </Box>
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>

            {/* BODY */}
            <TableBody
              sx={{
                "& .MuiTableRow-root": {
                  borderBottom: "2px solid #fff", // Horizontal grid line sa bawat row
                },
                "& .MuiTableRow-root:nth-of-type(odd)": {
                  backgroundColor: "#eceff1", // Alternate row color
                },
                // Specific alignment para sa bawat column gamit ang nth-of-type
                "& .MuiTableRow-root > .MuiTableCell-root:nth-of-type(1)": {
                  textAlign: "left",
                },
                "& .MuiTableRow-root > .MuiTableCell-root:nth-of-type(2)": {
                  textAlign: "center",
                },
                "& .MuiTableRow-root > .MuiTableCell-root:nth-of-type(3)": {
                  textAlign: "center",
                },
                "& .MuiTableRow-root > .MuiTableCell-root:nth-of-type(4)": {
                  textAlign: "right",
                },
                "& .MuiTableRow-root > .MuiTableCell-root:nth-of-type(5)": {
                  textAlign: "center",
                },
                "& .MuiTableCell-root": {
                  fontWeight: "normal", // Hindi naka-bold para sa body
                },
              }}
            >
              {isLoading ? (
                <TableBodyLoading numCell={columns.length} />
              ) : (
                <>
                  {/* Inline Add Row */}
                  <InlineOvertimeAddRow refreshData={refreshData} employeeOptions={employeeOptions} />

                  {/* Existing Rows */}
                  {rows.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={columns.length} align="center">
                        {searchValue ? (
                          <>
                            No results found for <b>"{searchValue}"</b>.
                          </>
                        ) : (
                          "No rows found."
                        )}
                      </TableCell>
                    </TableRow>
                  ) : (
                    rows.map((row) => (
                      <OvertimePayEditableRow
                        key={row._id}
                        row={row}
                        refreshData={refetch}
                        activeSettings={data?.activeSettings || null}
                      />
                    ))
                  )}
                </>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* PAGINATION */}
        <TablePagination
          rowsPerPageOptions={[10, ROWS_PER_PAGE, 50]}
          component="div"
          count={data?.totalRecords || 0}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />

        {/* FILTER POPOVER */}
        <Popover
          open={Boolean(filterAnchorEl)}
          anchorEl={filterAnchorEl}
          onClose={handleFilterClose}
          anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
        >
          <Box sx={{ display: "flex", flexDirection: "column", gap: 1, p: 1 }}>
            <Box sx={{ fontSize: 14, fontWeight: 600 }}>
              Filter by {fieldAndValue.label}
            </Box>
            {renderFilter()}
          </Box>
        </Popover>
      </Paper>
    </Box>
  );
};

export default OvertimePayCustomTable;
