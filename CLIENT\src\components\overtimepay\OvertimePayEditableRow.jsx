import React, { useState, useEffect, useCallback } from "react";
import { TableRow, TableCell, TextField, IconButton } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import api from "../../config/api";
import { toast } from "react-hot-toast";
import EditIcon from "@mui/icons-material/Edit";
import SaveIcon from "@mui/icons-material/Save";
import CancelIcon from "@mui/icons-material/Close";
import DeleteIcon from "@mui/icons-material/Delete";

const OvertimePayEditableRow = ({ row, refreshData, activeSettings: propActiveSettings }) => {
  const [activeSettings, setActiveSettings] = useState(propActiveSettings);
  const [personStatus, setPersonStatus] = useState(null);

  // Fetch active settings if not provided
  useEffect(() => {
    if (!activeSettings) {
      const fetchSettings = async () => {
        try {
          const res = await api.get("/settings/active");
          setActiveSettings(res.data);
        } catch (err) {
          console.error("Error fetching active settings:", err);
          toast.error("Failed to load settings.");
        }
      };
      fetchSettings();
    }
  }, [activeSettings]);

  // After settings load, fetch personnel status
  useEffect(() => {
    if (activeSettings && activeSettings.fiscalYear) {
      const fetchStatus = async () => {
        try {
          const res = await api.get(`/getpersonnels?fiscalYear=${activeSettings.fiscalYear}`);
          const found = res.data.find(
            (e) => e.employeeFullName === row.employeeFullName
          );
          setPersonStatus(found?.status || null);
        } catch (err) {
          console.error("Error fetching personnel status:", err);
        }
      };
      fetchStatus();
    }
  }, [activeSettings, row.employeeFullName]);

  // Settings multipliers
  const localSettings = activeSettings || { weekdayMultiplier: 1.25, weekendMultiplier: 1.5 };

  const getMonthlySalary = () => {
    const salary = Number(row.monthlySalary);
    return salary > 0 ? salary : 50000;
  };

  const [isEditing, setIsEditing] = useState(false);
  const [weekdayHours, setWeekdayHours] = useState(row.weekdayHours);
  const [weekendHours, setWeekendHours] = useState(row.weekendHours);
  const [computedAmount, setComputedAmount] = useState(row.amount);

  // Compute new amount
  const computeAmount = useCallback(() => {
    const monthlySalary = getMonthlySalary();
    const wd = Math.min(Number(weekdayHours) || 0, 3);
    const we = Math.min(Number(weekendHours) || 0, 8);
    const amount =
      wd * (localSettings.weekdayMultiplier / 100) * monthlySalary * 12 +
      we * (localSettings.weekendMultiplier / 100) * monthlySalary * 12;
    return isNaN(amount) ? 0 : amount;
  }, [weekdayHours, weekendHours, localSettings, row.monthlySalary]);

  useEffect(() => {
    if (isEditing) {
      setComputedAmount(computeAmount());
    }
  }, [computeAmount, isEditing]);

  const mutation = useMutation({
    mutationFn: async (data) => await api.put(`/overtime-pay/${row._id}`, data),
    onSuccess: () => {
      toast.success("Overtime record updated successfully");
      setIsEditing(false);
      refreshData();
    },
    onError: (err) => {
      toast.error(err.response?.data?.message || "Error updating record");
    },
  });

  const deleteMutation = useMutation({
    mutationFn: async () => await api.delete(`/overtime-pay/${row._id}`),
    onSuccess: () => {
      toast.success("Overtime record deleted successfully");
      refreshData();
    },
    onError: (err) => {
      toast.error(err.response?.data?.message || "Error deleting record");
    },
  });

  const handleSave = () => {
    const payload = {
      weekdayHours: Number(weekdayHours),
      weekendHours: Number(weekendHours),
      monthlySalary: getMonthlySalary(),
      amount: computeAmount(),
    };
    mutation.mutate(payload);
  };

  const handleDelete = () => {
    if (window.confirm("Are you sure you want to delete this record?")) {
      deleteMutation.mutate();
    }
  };

  // disable actions if status is Submitted or Approved
  const isFinal = ["Submitted", "Approved"].includes(personStatus);

  return (
    <TableRow>
      <TableCell>{row.employeeFullName}</TableCell>
      <TableCell>
        {isEditing ? (
          <TextField
            type="number"
            value={weekdayHours}
            onChange={(e) => setWeekdayHours(e.target.value)}
            variant="standard"
          />
        ) : (
          row.weekdayHours
        )}
      </TableCell>
      <TableCell>
        {isEditing ? (
          <TextField
            type="number"
            value={weekendHours}
            onChange={(e) => setWeekendHours(e.target.value)}
            variant="standard"
          />
        ) : (
          row.weekendHours
        )}
      </TableCell>
      <TableCell>
        {isEditing ? (
          <TextField
            value={computedAmount.toLocaleString("en-PH", { style: "currency", currency: "PHP" })}
            variant="standard"
            disabled
          />
        ) : (
          row.amount.toLocaleString("en-PH", { style: "currency", currency: "PHP" })
        )}
      </TableCell>
      <TableCell>
        {isEditing ? (
          <>
            <IconButton onClick={handleSave} color="primary" disabled={isFinal}>
              <SaveIcon />
            </IconButton>
            <IconButton onClick={() => setIsEditing(false)} color="secondary" disabled={isFinal}>
              <CancelIcon />
            </IconButton>
          </>
        ) : (
          <>
            <IconButton
              onClick={() => setIsEditing(true)}
              color="primary"
              disabled={isFinal}
            >
              <EditIcon />
            </IconButton>
            <IconButton
              onClick={handleDelete}
              color="error"
              disabled={isFinal}
            >
              <DeleteIcon />
            </IconButton>
          </>
        )}
      </TableCell>
    </TableRow>
  );
};

export default OvertimePayEditableRow;
