import React, { useEffect } from "react";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  TextField,
  Typography,
  Divider,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { NumericFormat } from "react-number-format";
import api from "../../config/api";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

// Component for numeric input formatting (for monetary values)
const NumberFormatCustom = React.forwardRef(function NumberFormatCustom(props, ref) {
  const { onChange, onBlur, ...other } = props;
  return (
    <NumericFormat
      {...other}
      getInputRef={ref}
      thousandSeparator
      decimalScale={2}
      fixedDecimalScale
      onValueChange={(values) => onChange(values.value)}
      onBlur={(e) => onBlur && onBlur(e)}
    />
  );
});

// Component for percentage input formatting (for PhilHealth, GSIS, etc.)
const PercentageFormatCustom = React.forwardRef(function PercentageFormatCustom(props, ref) {
  const { onChange, onBlur, ...other } = props;
  return (
    <NumericFormat
      {...other}
      getInputRef={ref}
      thousandSeparator
      decimalScale={2}
      suffix="%"
      onValueChange={(values) => onChange(values.floatValue / 100)} // Convert 5% to 0.05
      value={props.value * 100} // Display 0.05 as 5%
      onBlur={(e) => onBlur && onBlur(e)}
    />
  );
});

// Add this component for earned leaves input
const EarnedLeavesFormatCustom = React.forwardRef(function EarnedLeavesFormatCustom(props, ref) {
  const { onChange, onBlur, ...other } = props;
  return (
    <NumericFormat
      {...other}
      getInputRef={ref}
      thousandSeparator
      decimalScale={8} // Allow up to 8 decimal places
      fixedDecimalScale={false} // Allow variable decimal places
      allowNegative={false}
      onValueChange={(values) => onChange(values.floatValue || 0)}
      onBlur={(e) => onBlur && onBlur(e)}
    />
  );
});

// Validation schema
const advancedSchema = yup.object().shape({
  PERA: yup.number().min(0),
  uniformAllowance: yup.number().min(0),
  productivityIncentive: yup.number().min(0),
  medicalAllowance: yup.number().min(0),
  cashGift: yup.number().min(0),
  meal: yup.number().min(0),
  courtAppearance: yup.number().min(0),
  childrenAllowance: yup.number().min(0),
  gsisPremium: yup.number().min(0),
  philhealthPremium: yup
    .number()
    .min(0.05, "PhilHealth premium must be 5% in 2025 per Circular 2019-0009")
    .max(0.05, "PhilHealth premium must be 5% in 2025 per Circular 2019-0009")
    .required("PhilHealth premium is required")
    .typeError("Must be a valid number"),
  pagibigPremium: yup.number().min(0),
  employeeCompensation: yup.number().min(0),
  weekdayMultiplier: yup.number().min(0),
  weekendMultiplier: yup.number().min(0),
  subsistenceAllowanceRate: yup.number().min(0),
  earnedLeaves: yup.number().min(0),
  loyaltyPay: yup.object({
    baseYears: yup.number(),
    baseAmount: yup.number(),
    succeedingInterval: yup.number(),
    succeedingAmount: yup.number(),
  }),
  subsistenceAllowanceSTRates: yup.object({
    highRisk: yup.object({
      fifteenOrMoreDays: yup.number().min(0),
      eightToFourteenDays: yup.number().min(0),
      lessThanEightDays: yup.number().min(0),
    }),
    lowRisk: yup.object({
      fifteenOrMoreDays: yup.number().min(0),
      eightToFourteenDays: yup.number().min(0),
      lessThanEightDays: yup.number().min(0),
    }),
  }),
});

// Generic Accordion Section Component
const AccordionSection = ({ title, children }) => (
  <Accordion defaultExpanded>
    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
      <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
        {title}
      </Typography>
    </AccordionSummary>
    <AccordionDetails>{children}</AccordionDetails>
  </Accordion>
);

// Compensation Section (including subsistenceAllowanceRate)
const CompensationSection = ({ control, errors }) => {
  const fields = [
    { name: "PERA", label: "PERA (₱)" },
    { name: "uniformAllowance", label: "Uniform Allowance (₱)" },
    { name: "productivityIncentive", label: "Productivity Incentive (₱)" },
    { name: "medicalAllowance", label: "Medical Allowance (₱)" },
    { name: "cashGift", label: "Cash Gift (₱)" },
    { name: "meal", label: "Meal Allowance (₱)" },
    { name: "courtAppearance", label: "Court Appearance (₱)" },
    { name: "childrenAllowance", label: "Children Allowance (₱)" },
    { name: "subsistenceAllowanceRate", label: "Subsistence Allowance Rate" },
    { name: "earnedLeaves", label: "Earned Leaves (Days)", special: true },
  ];
  return (
    <AccordionSection title="Compensation">
      <Grid container spacing={2}>
        {fields.map(({ name, label, special }) => (
          <Grid item xs={6} key={name}>
            <Controller
              name={name}
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label={label}
                  fullWidth
                  InputProps={{
                    inputComponent: special ? EarnedLeavesFormatCustom : NumberFormatCustom,
                    style: { textAlign: "right" },
                  }}
                  error={!!errors[name]}
                  helperText={errors[name]?.message}
                />
              )}
            />
          </Grid>
        ))}
      </Grid>
    </AccordionSection>
  );
};

// Government Share Section
const GovernmentShareSection = ({ control, errors }) => {
  const fields = [
    { name: "gsisPremium", label: "GSIS Premium", inputComponent: PercentageFormatCustom },
    { name: "philhealthPremium", label: "PhilHealth Premium", inputComponent: PercentageFormatCustom },
    { name: "pagibigPremium", label: "Pag-IBIG Premium", inputComponent: NumberFormatCustom },
    { name: "employeeCompensation", label: "Employee Compensation", inputComponent: NumberFormatCustom },
  ];
  return (
    <AccordionSection title="Government Share">
      <Grid container spacing={2}>
        {fields.map(({ name, label, inputComponent }) => (
          <Grid item xs={6} key={name}>
            <Controller
              name={name}
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label={label}
                  fullWidth
                  InputProps={{
                    inputComponent,
                    inputProps: { value: field.value },
                    style: { textAlign: "right" },
                  }}
                  error={!!errors[name]}
                  helperText={
                    errors[name]?.message ||
                    (name === "philhealthPremium"
                      ? "Fixed at 5% for 2025 per PhilHealth Circular 2019-0009."
                      : "")
                  }
                />
              )}
            />
          </Grid>
        ))}
      </Grid>
    </AccordionSection>
  );
};

// Subsistence Allowance (S&T) Rates Section
const SubsistenceAllowanceSTRatesSection = ({ control, errors }) => {
  const highRiskFields = [
    { name: "subsistenceAllowanceSTRates.highRisk.fifteenOrMoreDays", label: "≥15 days" },
    { name: "subsistenceAllowanceSTRates.highRisk.eightToFourteenDays", label: "8–14 days" },
    { name: "subsistenceAllowanceSTRates.highRisk.lessThanEightDays", label: "<8 days" },
  ];
  const lowRiskFields = [
    { name: "subsistenceAllowanceSTRates.lowRisk.fifteenOrMoreDays", label: "≥15 days" },
    { name: "subsistenceAllowanceSTRates.lowRisk.eightToFourteenDays", label: "8–14 days" },
    { name: "subsistenceAllowanceSTRates.lowRisk.lessThanEightDays", label: "<8 days" },
  ];
  return (
    <AccordionSection title="Subsistence Allowance (S&T) Rates">
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Typography variant="subtitle2" fontWeight="bold">
            High Risk
          </Typography>
        </Grid>
        {highRiskFields.map(({ name, label }) => (
          <Grid item xs={4} key={name}>
            <Controller
              name={name}
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label={label}
                  type="number"
                  fullWidth
                  inputProps={{ step: 0.01, style: { textAlign: "right" } }}
                  error={!!errors?.subsistenceAllowanceSTRates?.highRisk?.[name.split(".").pop()]}
                  helperText={
                    errors?.subsistenceAllowanceSTRates?.highRisk?.[name.split(".").pop()]?.message
                  }
                />
              )}
            />
          </Grid>
        ))}
        <Grid item xs={12} sx={{ mt: 2 }}>
          <Typography variant="subtitle2" fontWeight="bold">
            Low Risk
          </Typography>
        </Grid>
        {lowRiskFields.map(({ name, label }) => (
          <Grid item xs={4} key={name}>
            <Controller
              name={name}
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label={label}
                  type="number"
                  fullWidth
                  inputProps={{ step: 0.01, style: { textAlign: "right" } }}
                  error={!!errors?.subsistenceAllowanceSTRates?.lowRisk?.[name.split(".").pop()]}
                  helperText={
                    errors?.subsistenceAllowanceSTRates?.lowRisk?.[name.split(".").pop()]?.message
                  }
                />
              )}
            />
          </Grid>
        ))}
      </Grid>
    </AccordionSection>
  );
};

// Percentage Multipliers Section
const PercentageMultipliersSection = ({ control, errors }) => {
  const fields = [
    {
      name: "weekdayMultiplier",
      label: "Weekday Multiplier (%)",
      type: "number",
      extraProps: { inputProps: { step: 0.01, style: { textAlign: "right" } } },
    },
    {
      name: "weekendMultiplier",
      label: "Weekend Multiplier (%)",
      type: "number",
      extraProps: { inputProps: { step: 0.01, style: { textAlign: "right" } } },
    },
  ];

  return (
    <AccordionSection title="Overtime Pay (Percentage Multipliers)">
      <Grid container spacing={2}>
        {fields.map(({ name, label, type, extraProps }) => (
          <Grid item xs={6} key={name}>
            <Controller
              name={name}
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label={label}
                  type={type || "number"}
                  fullWidth
                  InputProps={{
                    inputComponent: NumberFormatCustom,
                    ...extraProps,
                  }}
                  onBlur={field.onBlur}
                  error={!!errors[name]}
                  helperText={errors[name]?.message}
                />
              )}
            />
          </Grid>
        ))}
      </Grid>
    </AccordionSection>
  );
};

// Loyalty Pay Settings Section
const LoyaltyPaySettingsSection = ({ control, errors }) => (
  <AccordionSection title="Loyalty Pay Settings">
    <Grid container spacing={2}>
      <Grid item xs={6}>
        <Controller
          name="loyaltyPay.baseYears"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              label="Base Years (e.g. 10)"
              type="number"
              fullWidth
              InputProps={{
                inputComponent: NumberFormatCustom,
                style: { textAlign: "right" },
              }}
              error={!!errors?.loyaltyPay?.baseYears}
              helperText={errors?.loyaltyPay?.baseYears?.message}
            />
          )}
        />
      </Grid>
      <Grid item xs={6}>
        <Controller
          name="loyaltyPay.baseAmount"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              label="Base Amount (₱)"
              fullWidth
              InputProps={{
                inputComponent: NumberFormatCustom,
                style: { textAlign: "right" },
              }}
              error={!!errors?.loyaltyPay?.baseAmount}
              helperText={errors?.loyaltyPay?.baseAmount?.message}
            />
          )}
        />
      </Grid>
      <Grid item xs={6}>
        <Controller
          name="loyaltyPay.succeedingInterval"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              label="Succeeding Interval (e.g. 5 years)"
              type="number"
              fullWidth
              InputProps={{
                inputComponent: NumberFormatCustom,
                style: { textAlign: "right" },
              }}
              error={!!errors?.loyaltyPay?.succeedingInterval}
              helperText={errors?.loyaltyPay?.succeedingInterval?.message}
            />
          )}
        />
      </Grid>
      <Grid item xs={6}>
        <Controller
          name="loyaltyPay.succeedingAmount"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              label="Succeeding Amount (₱)"
              fullWidth
              InputProps={{
                inputComponent: NumberFormatCustom,
                style: { textAlign: "right" },
              }}
              error={!!errors?.loyaltyPay?.succeedingAmount}
              helperText={errors?.loyaltyPay?.succeedingAmount?.message}
            />
          )}
        />
      </Grid>
    </Grid>
  </AccordionSection>
);

// Main Dialog Component with integrated form
const CompensationSettingsDialogForm = ({
  fiscalYear,
  onSaved,
  onCancel,
  editData,
  open,
  onClose,
}) => {
  const queryClient = useQueryClient();
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(advancedSchema),
    defaultValues: {
      PERA: 0,
      uniformAllowance: 0,
      productivityIncentive: 0,
      medicalAllowance: 0,
      cashGift: 0,
      meal: 0,
      courtAppearance: 0,
      childrenAllowance: 0,
      gsisPremium: 0,
      philhealthPremium: 0.05, // Default to 5% for 2025
      pagibigPremium: 0,
      employeeCompensation: 0,
      weekdayMultiplier: 0,
      weekendMultiplier: 0,
      subsistenceAllowanceRate: 0,
      loyaltyPay: {
        baseYears: 0,
        baseAmount: 0,
        succeedingInterval: 0,
        succeedingAmount: 0,
      },
      subsistenceAllowanceSTRates: {
        highRisk: {
          fifteenOrMoreDays: 0,
          eightToFourteenDays: 0,
          lessThanEightDays: 0,
        },
        lowRisk: {
          fifteenOrMoreDays: 0,
          eightToFourteenDays: 0,
          lessThanEightDays: 0,
        },
      },
    },
  });

  // Pre-fill form if in edit mode
  useEffect(() => {
    if (editData) {
      reset({
        PERA: editData.PERA,
        uniformAllowance: editData.uniformAllowance,
        productivityIncentive: editData.productivityIncentive,
        medicalAllowance: editData.medicalAllowance,
        cashGift: editData.cashGift,
        meal: editData.meal,
        courtAppearance: editData.courtAppearance,
        childrenAllowance: editData.childrenAllowance,
        gsisPremium: editData.gsisPremium,
        philhealthPremium: editData.philhealthPremium,
        pagibigPremium: editData.pagibigPremium,
        employeeCompensation: editData.employeeCompensation,
        weekdayMultiplier: editData.weekdayMultiplier,
        weekendMultiplier: editData.weekendMultiplier,
        subsistenceAllowanceRate: editData.subsistenceAllowanceRate,
        earnedLeaves: editData.earnedLeaves,
        loyaltyPay: {
          baseYears: editData.loyaltyPay?.baseYears,
          baseAmount: editData.loyaltyPay?.baseAmount,
          succeedingInterval: editData.loyaltyPay?.succeedingInterval,
          succeedingAmount: editData.loyaltyPay?.succeedingAmount,
        },
        subsistenceAllowanceSTRates: {
          highRisk: {
            fifteenOrMoreDays: editData.subsistenceAllowanceSTRates?.highRisk?.fifteenOrMoreDays,
            eightToFourteenDays: editData.subsistenceAllowanceSTRates?.highRisk?.eightToFourteenDays,
            lessThanEightDays: editData.subsistenceAllowanceSTRates?.highRisk?.lessThanEightDays,
          },
          lowRisk: {
            fifteenOrMoreDays: editData.subsistenceAllowanceSTRates?.lowRisk?.fifteenOrMoreDays,
            eightToFourteenDays: editData.subsistenceAllowanceSTRates?.lowRisk?.eightToFourteenDays,
            lessThanEightDays: editData.subsistenceAllowanceSTRates?.lowRisk?.lessThanEightDays,
          },
        },
      });
    }
  }, [editData, reset]);

  // Mutation definition
  const mutation = useMutation({
    mutationFn: async (data) => {
      const effectiveFiscalYear = fiscalYear || editData?.fiscalYear;
      if (!effectiveFiscalYear) {
        throw new Error("Fiscal year is missing.");
      }
      const res = await api.put(`/settings/advanced/${effectiveFiscalYear}`, data);
      return res.data.message;
    },
    onSuccess: (message) => {
      queryClient.invalidateQueries(["settings"]);
      const updatedMessage = message.replace("Advanced settings", "Compensation Settings");
      toast.success(updatedMessage);
      if (onSaved) onSaved();
      reset();
      onClose();
    },
    onError: (error) => {
      toast.error(error.message || "An error occurred.");
    },
  });

  const onSubmit = (data) => {
    const effectiveFiscalYear = fiscalYear || editData?.fiscalYear;
    if (!effectiveFiscalYear) {
      toast.error("Fiscal year is missing. Please provide a valid fiscal year.");
      return;
    }
    mutation.mutate(data);
  };

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editData ? "Compensation Settings" : "Add Compensation Settings"}
        </DialogTitle>
        <DialogContent dividers>
          <CompensationSection control={control} errors={errors} />
          <Divider sx={{ my: 2 }} />
          <GovernmentShareSection control={control} errors={errors} />
          <Divider sx={{ my: 2 }} />
          <SubsistenceAllowanceSTRatesSection control={control} errors={errors} />
          <Divider sx={{ my: 2 }} />
          <PercentageMultipliersSection control={control} errors={errors} />
          <Divider sx={{ my: 2 }} />
          <LoyaltyPaySettingsSection control={control} errors={errors} />
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose} variant="outlined">
            Close
          </Button>
          <Button onClick={handleSubmit(onSubmit)} variant="contained" disabled={mutation.isLoading}>
            {mutation.isLoading ? "Saving..." : editData ? "Save" : "Save"}
          </Button>
        </DialogActions>
      </Dialog>
      <ToastContainer />
    </>
  );
};

export default CompensationSettingsDialogForm;
