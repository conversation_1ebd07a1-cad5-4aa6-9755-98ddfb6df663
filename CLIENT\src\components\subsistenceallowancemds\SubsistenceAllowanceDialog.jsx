import React, { useEffect, useState } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  TextField,
  Autocomplete,
  MenuItem,
} from "@mui/material";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import api from "../../config/api";
import { toast } from "react-hot-toast";
import { useUser } from "../../context/UserContext";
import EditIcon from "@mui/icons-material/Edit";
import CustomButton from "../../global/components/CustomButton";

const SubsistenceAllowanceDialog = ({ row, endpoint, dataListName, schema }) => {
  const isEditing = Boolean(row);
  const [open, setOpen] = useState(false);
  const queryClient = useQueryClient();
  const [employees, setEmployees] = useState([]);
  const { currentUser } = useUser();
  const [subsistenceRate, setSubsistenceRate] = useState(0);
  const [fiscalYearFromSettings, setFiscalYearFromSettings] = useState("");
  const [budgetTypeFromSettings, setBudgetTypeFromSettings] = useState("");

  const { control, handleSubmit, reset, watch, setValue } = useForm({
    defaultValues: {
      employee: row?.employee || null,
      positionTitle: row?.positionTitle || "",
    },
  });

  const selectedEmployee = watch("employee");

  const mutation = useMutation({
    mutationFn: async (data) => {
      const processBy = `${currentUser.FirstName} ${currentUser.LastName}`;
      const processDate = new Date();
      const fiscalYear = fiscalYearFromSettings;
      const budgetType = budgetTypeFromSettings;
      const payload = {
        employeeNumber: isEditing ? row.employeeNumber : data.employee.employeeNumber,
        employeeFullName: isEditing ? row.employeeFullName : data.employee.employeeFullName,
        positionTitle: isEditing ? row.positionTitle : data.employee.positionTitle,
        department: isEditing ? row.department : data.employee.department,
        division: isEditing ? row.division : data.employee.division,
        region: isEditing ? row.region : data.employee.region,
        Amount: subsistenceRate,
        fiscalYear,
        processBy,
        processDate,
        budgetType,
      };

      return isEditing
        ? await api.put(`${endpoint}/${row._id}`, payload)
        : await api.post(endpoint, payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries([dataListName]);
      toast.success(isEditing ? "Record updated" : "Record created");
      handleClose();
    },
    onError: (err) => {
      console.error("❌ Error submitting:", err);
      toast.error(err.response?.data?.error || "Something went wrong");
    },
  });

  const fetchEmployees = async () => {
    try {
      const res = await api.get("/getpersonnelsmds");
      setEmployees(res.data);
    } catch (error) {
      console.error("Error fetching employees:", error);
    }
  };

  const fetchActiveSettings = async () => {
    try {
      const res = await api.get("/settings/active");
      setSubsistenceRate(res.data.subsistenceAllowanceRate);
      setFiscalYearFromSettings(res.data.fiscalYear || new Date().getFullYear().toString());
    } catch (error) {
      console.error("Error fetching settings:", error);
    }
  };

  useEffect(() => {
    fetchEmployees();
    fetchActiveSettings();
  }, []);

  useEffect(() => {
    if (selectedEmployee && !isEditing) {
      setValue("positionTitle", selectedEmployee.positionTitle);
    }
  }, [selectedEmployee, setValue, isEditing]);

  const onSubmit = (data) => {
    mutation.mutate(data);
  };

  const handleOpen = () => setOpen(true);
  const handleClose = () => {
    reset();
    setOpen(false);
  };

  // Preprocess employees array to attach a unique key for each option
  const employeeOptions = employees
    .filter((e) => e.employeeFullName)
    .map((employee, index) => ({
      ...employee,
      uniqueKey: `${employee.employeeNumber}-${index}`,
    }));

  return (
    <>
      {!row ? (
        <CustomButton onClick={handleOpen} size="large">
          Add Subsistence
        </CustomButton>
      ) : (
        <MenuItem onClick={handleOpen} disableRipple sx={{ display: "flex", gap: 1 }}>
          <EditIcon fontSize="small" />
          Edit
        </MenuItem>
      )}

      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogTitle>
          {isEditing ? "Edit Subsistence Allowance" : "Add Subsistence Allowance"}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Controller
                name="employee"
                control={control}
                render={({ field }) => (
                  <Autocomplete
                    options={employeeOptions}
                    getOptionLabel={(option) => option.employeeFullName || ""}
                    isOptionEqualToValue={(option, value) =>
                      option.employeeNumber === value.employeeNumber
                    }
                    value={
                      isEditing
                        ? { employeeFullName: row.employeeFullName }
                        : field.value || null
                    }
                    onChange={(e, value) => !isEditing && field.onChange(value)}
                    renderInput={(params) => (
                      <TextField {...params} label="Select Employee" fullWidth disabled={isEditing} />
                    )}
                    renderOption={(props, option) => (
                      <li {...props} key={option.uniqueKey}>
                        {option.employeeFullName}
                      </li>
                    )}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Controller
                name="positionTitle"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Position Title"
                    value={field.value || ""}
                    disabled
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Amount"
                value={subsistenceRate.toLocaleString("en-PH", {
                  style: "currency",
                  currency: "PHP",
                })}
                fullWidth
                disabled
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleSubmit(onSubmit)} variant="contained">
            {isEditing ? "Update" : "Save"}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default SubsistenceAllowanceDialog;
