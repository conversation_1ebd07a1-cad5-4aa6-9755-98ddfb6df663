import React, { useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  TextField,
  Autocomplete,
  MenuItem,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import EditIcon from "@mui/icons-material/Edit";
import CustomButton from "../../global/components/CustomButton";
import api from "../../config/api";
import { useUser } from "../../context/UserContext";

const SubsistenceAllowanceSTDialog = ({ row, endpoint, dataListName, schema }) => {
  const isEditing = Boolean(row);
  const [open, setOpen] = useState(false);
  const [employees, setEmployees] = useState([]);
  const [settings, setSettings] = useState(null);
  const { currentUser } = useUser();
  const queryClient = useQueryClient();

  const { control, handleSubmit, reset, watch, setValue } = useForm({
    defaultValues: {
      employee: row?.employee || null,
      positionTitle: row?.positionTitle || "",
      department: row?.department || "",
      division: row?.division || "",
      region: row?.region || "",
      monthlySalary: row?.monthlySalary || 0,
      actualExposureDays: row?.actualExposureDays || 0,
      riskLevel: row?.riskLevel || "",
    },
  });

  const selectedEmployee = watch("employee");
  const actualExposureDays = watch("actualExposureDays");
  const riskLevel = watch("riskLevel");
  const monthlySalary = watch("monthlySalary");

  useEffect(() => {
    fetchEmployees();
    fetchSettings();
  }, []);

  useEffect(() => {
    if (selectedEmployee && !isEditing) {
      setValue("positionTitle", selectedEmployee.positionTitle || "");
      setValue("department", selectedEmployee.department || "");
      setValue("division", selectedEmployee.division || "");
      setValue("region", selectedEmployee.region || "");
      setValue("monthlySalary", selectedEmployee.monthlySalary || 0);
    }
  }, [selectedEmployee, setValue, isEditing]);

  const fetchEmployees = async () => {
    try {
      const res = await api.get("/getpersonnels");
      setEmployees(res.data);
    } catch (err) {
      toast.error("Failed to fetch employees.");
    }
  };

  const fetchSettings = async () => {
    try {
      const res = await api.get("/settings/active");
      setSettings(res.data);
    } catch (err) {
      toast.error("Failed to fetch settings.");
    }
  };

  const computeAmount = () => {
    if (!settings || !monthlySalary || !actualExposureDays || !riskLevel) return 0;
    const rates = settings.subsistenceAllowanceSTRates;
    let percentage = 0;

    if (riskLevel === "High") {
      if (actualExposureDays >= 15) percentage = rates.highRisk.fifteenOrMoreDays;
      else if (actualExposureDays >= 8)
        percentage = rates.highRisk.eightToFourteenDays;
      else percentage = rates.highRisk.lessThanEightDays;
    } else {
      if (actualExposureDays >= 15) percentage = rates.lowRisk.fifteenOrMoreDays;
      else if (actualExposureDays >= 8)
        percentage = rates.lowRisk.eightToFourteenDays;
      else percentage = rates.lowRisk.lessThanEightDays;
    }

    return monthlySalary * percentage;
  };

  const mutation = useMutation({
    mutationFn: async (data) => {
      const processBy = `${currentUser.FirstName} ${currentUser.LastName}`;
      const processDate = new Date();
      const fiscalYear =
        settings?.fiscalYear || new Date().getFullYear().toString();
      const budgetType = settings?.budgetType;

      const payload = {
        employeeNumber: isEditing
          ? row.employeeNumber
          : data.employee?.employeeNumber,
        employeeFullName: isEditing
          ? row.employeeFullName
          : data.employee?.employeeFullName,
        positionTitle: data.positionTitle,
        department: data.department,
        division: data.division,
        region: data.region,
        monthlySalary: Number(data.monthlySalary),
        actualExposureDays: Number(data.actualExposureDays),
        riskLevel: data.riskLevel,
        amount: computeAmount(),
        fiscalYear,
        budgetType,
        processBy,
        processDate,
      };

      return isEditing
        ? await api.put(`${endpoint}/${row._id}`, payload)
        : await api.post(endpoint, payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries([dataListName]);
      toast.success(isEditing ? "Record updated" : "Record created");
      handleClose();
    },
    onError: (err) => {
      toast.error(err.response?.data?.error || "Something went wrong");
    },
  });

  const onSubmit = (data) => {
    mutation.mutate(data);
  };

  const handleOpen = () => setOpen(true);
  const handleClose = () => {
    reset();
    setOpen(false);
  };

  // Preprocess employees to attach a unique key for each option
  const employeeOptions = employees
    .filter((emp) => emp.employeeFullName)
    .map((emp, index) => ({
      ...emp,
      uniqueKey: emp._id ? `${emp._id}-${index}` : `unknown-${index}`,
    }));

  return (
    <>
      {!row ? (
        <CustomButton onClick={handleOpen} size="large">
          Add Subsistence (S&T)
        </CustomButton>
      ) : (
        <MenuItem onClick={handleOpen} disableRipple sx={{ display: "flex", gap: 1 }}>
          <EditIcon fontSize="small" />
          Edit
        </MenuItem>
      )}

      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogTitle>
          {isEditing ? "Edit S&T Allowance" : "Add S&T Allowance"}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            {/* Employee Selector */}
            <Grid item xs={12}>
              <Controller
                name="employee"
                control={control}
                render={({ field }) => (
                  <Autocomplete
                    options={employeeOptions}
                    getOptionLabel={(option) => option.employeeFullName || ""}
                    isOptionEqualToValue={(option, value) =>
                      option._id === value._id
                    }
                    value={
                      isEditing
                        ? employeeOptions.find(
                            (emp) => emp.employeeFullName === row.employeeFullName
                          ) || null
                        : field.value
                    }
                    onChange={(e, value) => !isEditing && field.onChange(value)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Select Employee"
                        fullWidth
                        disabled={isEditing}
                      />
                    )}
                    renderOption={(props, option) => (
                      <li {...props} key={option.uniqueKey}>
                        {option.employeeFullName}
                      </li>
                    )}
                  />
                )}
              />
            </Grid>

            {/* Auto-filled + Input Fields */}
            <Grid item xs={6}>
              <Controller
                name="positionTitle"
                control={control}
                render={({ field }) => (
                  <TextField {...field} label="Position Title" fullWidth disabled />
                )}
              />
            </Grid>
            <Grid item xs={6}>
              <Controller
                name="monthlySalary"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Monthly Salary"
                    type="number"
                    fullWidth
                    disabled
                  />
                )}
              />
            </Grid>

            <Grid item xs={6}>
              <Controller
                name="actualExposureDays"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Actual Exposure Days"
                    type="number"
                    fullWidth
                  />
                )}
              />
            </Grid>
            <Grid item xs={6}>
              <Controller
                name="riskLevel"
                control={control}
                render={({ field }) => (
                  <TextField {...field} label="Risk Level" select fullWidth>
                    <MenuItem value="High">High</MenuItem>
                    <MenuItem value="Low">Low</MenuItem>
                  </TextField>
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                label="Computed Amount"
                value={computeAmount().toLocaleString("en-PH", {
                  style: "currency",
                  currency: "PHP",
                })}
                fullWidth
                disabled
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleSubmit(onSubmit)} variant="contained">
            {isEditing ? "Update" : "Save"}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default SubsistenceAllowanceSTDialog;
