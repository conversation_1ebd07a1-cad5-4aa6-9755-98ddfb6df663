import { createContext, useContext, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { authApi } from "../config/api";
import Loading from "../global/components/Loading";
import env from "../utils/env";
import CryptoJS from "crypto-js"; // Import CryptoJS for encryption

const UserContext = createContext({
  currentUser: null,
  setCurrentUser: async () => {},
  isLoading: false,
  error: null,
  logout: async () => {},
});

const UserProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const nav = useNavigate();

  // Function to encrypt and store token
  const storeToken = (token) => {
    const encryptedToken = CryptoJS.AES.encrypt(
      token,
      env("SECRET_KEY")
    ).toString();
    localStorage.setItem("token", encryptedToken);
  };

  // Function to retrieve and decrypt token
  const getToken = () => {
    const encryptedToken = localStorage.getItem("token");
    if (!encryptedToken) return null;
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedToken, env("SECRET_KEY"));
      return bytes.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      console.error("Error decrypting token:", error);
      return null;
    }
  };

  // Fetch user and store token
  const handleSetUserInitialization = async () => {
    const res = await authApi.get("/get-user");
    setCurrentUser(res.data.user);
    if (res.data.jwtToken) {
      storeToken(res.data.jwtToken);
    }
  };

  const handleLogout = async () => {
    await authApi.get("/logout");
    setCurrentUser(null);
    localStorage.removeItem("token"); // Remove token on logout
    location.href = env("AUTH_CLIENT_URL") + "/login";
  };

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const res = await authApi.get("/get-user");
        if (res.data.user) {
          setCurrentUser(res.data.user);
          if (res.data.jwtToken) {
            storeToken(res.data.jwtToken);
          }
        } else {
          localStorage.removeItem("token");
          location.href = env("AUTH_CLIENT_URL") + "/login";
        }
      } catch (error) {
        localStorage.removeItem("token");
        console.error("Error fetching user:", error);
        location.href = env("AUTH_CLIENT_URL") + "/login";
      } finally {
        setIsLoading(false);
      }
    };
    fetchUser();
  }, [nav]);

  const value = {
    currentUser,
    setCurrentUser: handleSetUserInitialization,
    logout: handleLogout,
    isLoading,
    jwtToken: getToken(),
  };

  if (isLoading) return <Loading />;

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};

export default UserProvider;
export const useUser = () => useContext(UserContext);