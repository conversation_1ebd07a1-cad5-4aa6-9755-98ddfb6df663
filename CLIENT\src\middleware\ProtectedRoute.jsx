import React from "react";
import { Navigate } from "react-router-dom";
import { useUser } from "../context/UserContext";

const ProtectedRoute = ({ allowedRoles, children }) => {
  const { currentUser } = useUser();

  const hasAccess = Array.isArray(currentUser?.Roles)
    ? currentUser.Roles.some((role) => allowedRoles.includes(role))
    : allowedRoles.includes(currentUser?.Roles);

  return hasAccess ? children : <Navigate to="/not-authorized" />;
};

export default ProtectedRoute;