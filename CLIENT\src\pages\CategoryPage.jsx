import React, { useEffect, useState } from "react";
import CustomPage from "../components/category/CategoryCustomPage";
import CustomCreateUpdateDialog from "../components/category/CategoryCustomCreateUpdateDialog";
import api from "../config/api";

const CategoryPage = () => {
  const [sublineOptions, setSublineOptions] = useState([]);

  useEffect(() => {
    const fetchSublineItems = async () => {
      try {
        const res = await api.get("/categories/subline-items");
        setSublineOptions(res.data.sublineItems || []);
      } catch (err) {
        console.error("Failed to fetch subline items", err);
      }
    };

    fetchSublineItems();
  }, []);

  const categorySchema = {
    action: {
      type: "action",
      label: "Actions",
    },
    categoryName: {
      type: "text",
      label: "TITLE",
      required: true,
      searchable: true,
      show: true,
    },
    sublineItems: {
      type: "multi-select",
      label: "SUBLINE ITEMS",
      options: sublineOptions.map((item) => ({ label: item, value: item })),
      show: true,
      searchable: true,
      customRender: (row) => (
        <ul style={{ margin: 0, paddingLeft: 16 }}>
          {(row.sublineItems || []).map((item, index) => (
            <li key={index}>{item}</li>
          ))}
        </ul>
      ),
    },
  };

  return (
    <CustomPage
    dataListName="categories"
    schema={categorySchema}
    hasAdd={true}
    hasEdit={false}
    hasDelete={true}
    customAddElement={
      <CustomCreateUpdateDialog
        schema={categorySchema}
        endpoint="/categories"
        dataListName="categories"
      />
    }
    additionalMenuOptions={[
      ({ row, endpoint, dataListName }) => (
        <CustomCreateUpdateDialog
          row={row}
          schema={categorySchema}
          endpoint={endpoint}
          dataListName={dataListName}
        />
      ),
    ]}
  />
  
  );
};

export default CategoryPage;
