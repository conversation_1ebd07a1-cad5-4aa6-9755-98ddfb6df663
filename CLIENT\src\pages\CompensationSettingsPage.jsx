import { AiFillCreditCard } from "react-icons/ai"; 
// CompensationSettingsPage.jsx (sample lang)
import React, { useState } from "react";
import { useParams } from "react-router-dom";
import { Button } from "@mui/material";
import CustomPage from "../global/components/CustomPage";
import CompensationSettingsDialogForm from "../components/settings/CompensationSettingsForm";
import '../global/components/CreditCardIcon.css'

const CompensationSettingsPage = () => {
  const { fiscalYear } = useParams();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedData, setSelectedData] = useState(null);

  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedData(null);
  };

  const handleAddOrEdit = () => {
    // Halimbawa, kung gusto mong mag-check kung may existing compensation na
    // at kung meron, mag-editData = existingData, kung wala, null. 
    // Sa sample na ito, diretsong magse-set lang tayo:
    setSelectedData(null); 
    setDialogOpen(true);
  };

  // Schema para lang sa display sa CustomPage, optional
  const settingsSchema = {
    action: {
      type: "action",
      label: "Actions",
      customRender: (row) => (
        <AiFillCreditCard onClick={() => {
          setSelectedData(row);
            setDialogOpen(true);
          }}
          className="credit-card-icon"
          tabIndex="0"
        
          >
            
          </AiFillCreditCard>
          ),
        },
        fiscalYear: {
           type: "Text", 
           label: "Fiscal Year", 
           show: true 
          },
          isActive: {
            type: "boolean",
            label: "Active Fiscal Year",
            default: false,
            show: true,
            
          },
    // ...other fields kung gusto mong ipakita...
  };

  return (
    <>
      
      <CustomPage
        dataListName="settings"
        schema={settingsSchema}
        filter={{ fiscalYear }}
        hasEdit={false}
        hasAdd={false}
  customAddElement={
    <div style={{ display: "flex", justifyContent: "flex-end" }}>
      <Button variant="contained" onClick={handleAddOrEdit}>
        ADD COMPENSATION SETTINGS
      </Button>
    </div>
    
  }
  title="Compensation Settings"
  description="Manage Compensation"
  
/>
      {/* Gamitin na ang Integrated DialogForm */}
      <CompensationSettingsDialogForm
        open={dialogOpen}
        onClose={handleDialogClose}
        fiscalYear={fiscalYear}
        editData={selectedData}
        onSaved={handleDialogClose}
      />
    </>
  );
};

export default CompensationSettingsPage;
