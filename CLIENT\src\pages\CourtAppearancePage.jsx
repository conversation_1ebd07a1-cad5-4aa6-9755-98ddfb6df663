import React, { useEffect, useState } from "react";
import CustomPage from "../components/courtofappearance/CustomPage";
import api from "../config/api";
import { toast } from "react-hot-toast";
import { <PERSON><PERSON><PERSON>, Button, Stack } from "@mui/material";
import { useUser } from "../context/UserContext";
import SaveIcon from "@mui/icons-material/Save";
import { MdCancel } from "react-icons/md";

const EmployeeCourtAppearancePage = () => {
  const { currentUser } = useUser();

  const [settings, setSettings] = useState(null);
  const [tableData, setTableData] = useState([]);
  const [originalData, setOriginalData] = useState([]);
  const [inputErrors, setInputErrors] = useState({});

  // 1) Load active settings
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const res = await api.get("/settings/active");
        if (res.data) {
          setSettings(res.data);
        } else {
          toast.error("No active settings found.");
        }
      } catch (err) {
        toast.error("Failed to fetch settings.");
      }
    };
    fetchSettings();
  }, []);

  // 2) Fetch all data with a high limit
  const fetchData = async () => {
    try {
      const res = await api.get("/court-appearances", { params: { limit: 10000 } });
      const data = res.data.data || [];
      // Add isModified flag
      const dataWithFlag = data.map((row) => ({ ...row, isModified: false }));
      setTableData(dataWithFlag);
      setOriginalData(JSON.parse(JSON.stringify(dataWithFlag)));
    } catch (err) {
      toast.error("Failed to load court appearances.");
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // 3) Handle changes using employeeNumber as the unique key
  const handleChange = (employeeNumber, value) => {
    if (Number(value) < 0) {
      setInputErrors((prev) => ({ ...prev, [employeeNumber]: "Value cannot be negative" }));
      return;
    }
    setInputErrors((prev) => {
      const copy = { ...prev };
      delete copy[employeeNumber];
      return copy;
    });
    setTableData((prev) =>
      prev.map((row) => {
        if (row.employeeNumber === employeeNumber) {
          const num = Number(value);
          return {
            ...row,
            noOfCourtAppearance: isNaN(num) ? 0 : num,
            courtAppearanceAmount: settings ? num * settings.courtAppearance : 0,
            isModified: true,
          };
        }
        return row;
      })
    );
  };

  // 4) Save all modified rows
  const handleSaveAll = async () => {
    if (!settings) {
      toast.error("Settings not loaded.");
      return;
    }
    const processBy = `${currentUser.FirstName} ${currentUser.LastName}`;
    const processDate = new Date();
    const fiscalYear = settings.fiscalYear;
    const budgetType = settings.budgetType;

    try {
      const modified = tableData.filter((row) => row.isModified);
      if (modified.length === 0) {
        toast("No changes to save.");
        return;
      }
      await Promise.all(
        modified.map((row) => {
          const payload = {
            employeeNumber: row.employeeNumber,
            employeeFullName: row.employeeFullName,
            positionTitle: row.positionTitle,
            department: row.department,
            division: row.division,
            region: row.region,
            noOfCourtAppearance: row.noOfCourtAppearance,
            courtAppearanceAmount: row.noOfCourtAppearance * settings.courtAppearance,
            processBy,
            processDate,
            fiscalYear,
            budgetType,
          };
          if ((row._id || "").includes("_temp")) {
            return api.post("/court-appearances", payload);
          } else {
            return api.put(`/court-appearances/${row._id}`, payload);
          }
        })
      );
      toast.success("All changes saved.");
      fetchData();
    } catch (err) {
      console.error("Batch save error:", err);
      toast.error(err.response?.data?.message || "Error saving changes.");
    }
  };

  // 5) Cancel changes
  const handleCancel = () => {
    setTableData(JSON.parse(JSON.stringify(originalData)));
    setInputErrors({});
    toast("Changes discarded.");
  };

  // 6) The Save/Cancel buttons (walang role-based na restrictions)
  const headerButtons = (
    <Stack direction="row" spacing={2}>
      <Button 
        onClick={handleSaveAll} 
        color="success" 
        variant="contained"
        sx={{
          mr: 2,
          background: "#009688",
          color: "#fff",
          "&:hover": {
            background: "#00796B",
            color: "#fff",
            textDecoration: "underline rgb(255, 255, 255)"
          },
        }}
        startIcon={<SaveIcon />}
      >
        Save
      </Button>
      <Button 
        onClick={handleCancel} 
        color="warning" 
        variant="contained"
        sx={{
          mr: 2,
          background: "#9E9E9E",
          color: "#fff",
          "&:hover": {
            background: "#757575",
            color: "#fff",
            textDecoration: "underline rgb(255, 255, 255)"
          },
        }}
        startIcon={<MdCancel />}
      >
        Cancel
      </Button>
    </Stack>
  );

  // 7) Table schema
  const schema = {
    employeeNumber: { type: "text", label: "Employee Number" },
    employeeFullName: { type: "text", label: "Full Name", show: true },
    positionTitle: { type: "text", label: "Position Title", show: true },
    department: { type: "text", label: "Department" },
    division: { type: "text", label: "Division" },
    region: { type: "text", label: "Region" },
    noOfCourtAppearance: {
      type: "number",
      label: "No. of Court Appearances",
      show: true,
      customRender: (row) => (
        <>
          <TextField
            type="number"
            size="small"
            error={Boolean(inputErrors[row.employeeNumber])}
            helperText={inputErrors[row.employeeNumber]}
            inputProps={{ min: 0 }}
            value={row.noOfCourtAppearance ?? ""}
            onChange={(e) =>
              handleChange(
                row.employeeNumber,
                e.target.value === "" ? 0 : e.target.value
              )
            }
            disabled={
              row.status === "Submitted" ||
              row.status === "Approve" ||
              row.status === "Approved"
            }
          />
          {row.isModified && (
            <span style={{ color: "orange", fontSize: "0.8rem", marginLeft: 4 }}>
              *
            </span>
          )}
        </>
      ),
    },
    
    courtAppearanceAmount: {
      type: "number",
      label: "Court Appearance Amount",
      show: true,
      customRender: (row) =>
        `₱${(row.courtAppearanceAmount || 0).toLocaleString(undefined, {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        })}`,
    },
    fiscalYear: { type: "text", label: "Fiscal Year" },
    budgetType: { type: "text", label: "Budget Type" },
  };

  return (
    <CustomPage
      dataListName="court-appearances"
      schema={schema}
      hasAdd={false}
      tableData={tableData}
      customAddElement={headerButtons}
    />
  );
};

export default EmployeeCourtAppearancePage;
