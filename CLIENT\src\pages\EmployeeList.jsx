import React from "react";
import { format } from "date-fns";
import { Switch, Box, Typography } from "@mui/material";
import EmployeeCustomPage from "../components/employee/EmployeeCustomPage";
import TextSearchable from "../global/components/TextSearchable";
import api from "../config/api";
import { toast } from "react-hot-toast";

const EmployeeList = () => {
  const employeeSchema = {
    action: {
      type: "action",
      label: "Actions",
    },
    EmployeeFullName: {
      type: "text",
      label: "Employee Full Name",
      customRender: (row) => (
        <TextSearchable columnName={row.EmployeeFullName} />
      ),
      show: true,
    },
    Department: {
      type: "text",
      label: "Department",
      customRender: (row) => <TextSearchable columnName={row.Department} />,
      show: true,
    },
    Division: {
      type: "text",
      label: "Division",
      searchable: true,
      show: true,
    },
    Section: {
      type: "text",
      label: "Section",
      searchable: true,
      show: true,
    },
    Region: {
      type: "text",
      label: "Region",
      searchable: true,
      show: true,
    },
    PositionTitle: {
      type: "text",
      label: "Position Title",
      searchable: true,
      show: true,
    },
    StatusOfAppointment: {
      type: "text",
      label: "Status of Appointment",
      searchable: true,
      show: true,
    },
    SG: {
      type: "text",
      label: "SG",
      show: true,
    },
    Step: {
      type: "text",
      label: "Step",
      show: true,
    },
    JG: {
      type: "text",
      label: "JG",
      show: true,
    },
    Rate: {
      type: "number",
      label: "Rate",
      show: true,
    },
    DateOfAppointment: {
      type: "date",
      label: "Date of Appointment",
      searchable: true,
      show: true,
      customRender: (row) => {
        const date = new Date(row.DateOfAppointment);
        return format(date, "MMM dd, yyyy");
      },
    },
    employeeStatus: {
      type: "text",
      label: "Status",
      searchable: true,
      show: true,
      customRender: (row) => {
        const handleStatusToggle = async (checked) => {
          const newStatus = checked ? "Active" : "Inactive";
          try {
            await api.put(`/employees/${row._id}`, { employeeStatus: newStatus });
            toast.success(`Employee status updated to ${newStatus}`);
            return true;
          } catch (error) {
            toast.error("Failed to update employee status");
            console.error("Error updating status:", error);
            return false;
          }
        };

        return {
          render: (
            <Switch
              checked={row.employeeStatus === "Active"}
              onChange={async (e) => {
                const success = await handleStatusToggle(e.target.checked);
                return success;
              }}
              color="primary"
              inputProps={{ "aria-label": "employee status toggle" }}
            />
          ),
          onChange: handleStatusToggle,
        };
      },
    },
  };

  return (
    <>
      <EmployeeCustomPage
        dataListName="employees"
        title="Employee List"
        description="This is the Employee Table"
        schema={employeeSchema}
        searchable={true}
        hasEdit={true}
        hasDelete={false}
        ROWS_PER_PAGE={20}
        tableContainerProps={{ 
          sx: { height: 'calc(100vh - 200px)' } 
        }}
        footerElement={
          <Box 
            sx={{ 
              width: '100%',
              py: 0.5, 
              borderTop: '1px solid #ccc',
              textAlign: 'center',
              bgcolor: '#f5f5f5'
            }}
          >
            <Typography variant="caption" color="text.secondary">
              © {new Date().getFullYear()} NIA – ONLINE SUBMISSION OF BUDGET PROPOSAL INFORMATION SYSTEM
            </Typography>
          </Box>
        }
      />
    </>
  );
};

export default EmployeeList;
