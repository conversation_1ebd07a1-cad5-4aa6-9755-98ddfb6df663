// src/pages/FiscalYearSettingsPage.jsx
import React, { useState } from "react";
import AddSettingsDialog from "../components/settings/AddSettingsDialog";
import ProposalActionsButton from "../global/components/ProposalActionsButton";
import CustomPage from "../global/components/CustomPage";

import { Button } from "@mui/material";

const FiscalYearSettingsPage = () => {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  const handleOpenAddDialog = () => setIsAddDialogOpen(true);
  const handleCloseAddDialog = () => setIsAddDialogOpen(false);

  // Schema para sa basic fiscal settings
  const settingsSchema = {
    action: {
      type: "action",
      label: "Actions",
      customRender: (row) => <ProposalActionsButton row={row} />,
    },
    fiscalYear: {
      type: "Text",
      label: "Fiscal Year",
      required: true,
      unique: true,
      searchable: true,
      show: true,
    },
    budgetType: {
      type: "Text",
      label: "Budget Type",
      required: true,
      show: true,
    },
    startDate: {
      type: "date",
      label: "Start Date",
      required: true,
      show: true,
    },
    dueDate: {
      type: "date",
      label: "Due Date",
      required: true,
      show: true,
    },
    isActive: {
      type: "boolean",
      label: "Active Fiscal Year",
      default: false,
      show: true,
    },
  };

  return (
    <>
      <CustomPage
        dataListName="settings"
        schema={settingsSchema}
        hasEdit={false}
        customAddElement={
          <Button variant="contained" color="primary" onClick={handleOpenAddDialog}>
            Add Settings
          </Button>
        }
        title="Set Cut-Off Settings"
        description="Manage fiscal year settings"
      />
      {isAddDialogOpen && (
        <AddSettingsDialog open={isAddDialogOpen} onCloseDialog={handleCloseAddDialog} />
      )}
    </>
  );
};

export default FiscalYearSettingsPage;
