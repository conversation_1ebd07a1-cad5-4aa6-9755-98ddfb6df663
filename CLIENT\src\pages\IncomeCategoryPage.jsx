import React, { useEffect, useState } from "react";
import IncomeCategoriesCustomPage from "../components/incomecategories/IncomeCategoriesCustomPage";
import IncomeCategoriesCustomCreateUpdateDialog from "../components/incomecategories/IncomeCategoriesCustomCreateUpdateDialog";
import api from "../config/api";

const IncomeCategoryPage = () => {
  const [subcategoriesOptions, setSubcategoriesOptions] = useState([]);

  useEffect(() => {
    const fetchSubcategories = async () => {
      try {
        const res = await api.get("/income-categories/subcategories");
        setSubcategoriesOptions(res.data.incomeSubcategoryName || []);
      } catch (err) {
        console.error("Failed to fetch subcategories", err);
      }
    };

    fetchSubcategories();
  }, []);

const schema = {
  action: {
    type: "action",
    label: "Actions",
    show: true,
  },
  incomeCategoryName: {
    type: "text",
    label: "Category Name",
    searchable: true,
    show: true,
  },
  description: {
    type: "text",
    label: "Description",
    searchable: true,
    show: true,
  },
  incomeSubcategoryName: {
    type: "multi-select",
    label: "SubCategory Name",
    searchable: true,
    options: subcategoriesOptions.map((item) => ({ label: item, value: item })),
    show: true,

    customRender: (row) => {
      <ul style={{ margin: 0, padding: 16 }}>
        {(row.incomeSubcategoryName || []).map((item, index) => (
          <li key={index}>{item}</li>
        ))}
      </ul>;
    },
  },
};

  return (
    <IncomeCategoriesCustomPage
    dataListName="income-categories"
    schema={schema}
    hasAdd={true}
    hasEdit={false}
    hasDelete={true}
    customAddElement={
      <IncomeCategoriesCustomCreateUpdateDialog
        schema={schema}
        endpoint="/income-categories"
        dataListName="income-categories"
      />
    }
    additionalMenuOptions={[
      ({ row, endpoint, dataListName }) => (
        <IncomeCategoriesCustomCreateUpdateDialog
          row={row}
          schema={schema}
          endpoint={endpoint}
          dataListName={dataListName}
        />
      ),
    ]}
  />
  
  );
};

export default IncomeCategoryPage;