import React from "react";
import IncomeSubCategoriesCustomPage from "../components/incomesubcategories/IncomeSubCategoriesCustomPage";
import TextSearchable from "../global/components/TextSearchable";

const IncomeSubCategoryPage = () => {
  const IncomeSubCategorySchema = {
     action: {
      type: "action",
      label: "Actions",
    },
    incomeSubcategoryName: {
        type: "text",
        label: "Income SubCategory Name",
        required: true,
        searchable: true,
        show: true,
    },
   
  };
  return <IncomeSubCategoriesCustomPage 
  dataListName="income-subcategories" 
  schema={IncomeSubCategorySchema}
   
  />;
};

export default IncomeSubCategoryPage;