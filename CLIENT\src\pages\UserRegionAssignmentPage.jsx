import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  CircularProgress,
  Snackbar,
  Alert,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  OutlinedInput,
  Checkbox,
  ListItemText,
  TextField
} from '@mui/material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import api from '../config/api';
import DashboardHeader from '../global/components/DashboardHeader';
import { useUser } from '../context/UserContext';

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

const UserRegionAssignmentPage = () => {
  const { currentUser, jwtToken } = useUser();
  const queryClient = useQueryClient();
  const [selectedUser, setSelectedUser] = useState('');
  const [selectedRegions, setSelectedRegions] = useState([]);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Replace the users fetch with a manual input form
  const [users, setUsers] = useState([]);
  const [newUser, setNewUser] = useState({ id: '', name: '', email: '' });

  // Add this function to handle adding a new user
  const handleAddUser = () => {
    if (!newUser.id || !newUser.name || !newUser.email) {
      setSnackbar({
        open: true,
        message: 'Please fill in all user fields',
        severity: 'warning'
      });
      return;
    }
    
    setUsers([...users, newUser]);
    setNewUser({ id: '', name: '', email: '' });
  };

  // Replace the users fetch query with this
  const loadingUsers = false;

  // Fetch all regions
  const { data: regions = [], isLoading: loadingRegions } = useQuery({
    queryKey: ['regions'],
    queryFn: async () => {
      try {
        const res = await api.get('/api/regions');
        // Ensure we return an array even if the API returns something else
        return Array.isArray(res.data) ? res.data : [];
      } catch (error) {
        console.error('Error fetching regions:', error);
        return [];
      }
    }
  });

  // Fetch user-region assignments
  const { data: assignments = [], isLoading: loadingAssignments } = useQuery({
    queryKey: ['userRegionAssignments'],
    queryFn: async () => {
      try {
        const res = await api.get('/api/user-region-assignments');
        // Ensure we return an array even if the API returns something else
        return Array.isArray(res.data) ? res.data : [];
      } catch (error) {
        console.error('Error fetching assignments:', error);
        return [];
      }
    }
  });

  // Mutation for creating/updating assignments
  const createOrUpdateMutation = useMutation({
    mutationFn: async (data) => {
      return api.post('/api/user-region-assignments', data, {
        headers: {
          Authorization: `Bearer ${jwtToken}`
        }
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['userRegionAssignments']);
      setSnackbar({
        open: true,
        message: 'User region assignment saved successfully',
        severity: 'success'
      });
    },
    onError: (error) => {
      console.error('Error saving assignment:', error);
      setSnackbar({
        open: true,
        message: 'Failed to save user region assignment',
        severity: 'error'
      });
    }
  });

  // Mutation for deleting assignments
  const deleteMutation = useMutation({
    mutationFn: async (userId) => {
      return api.delete(`/api/user-region-assignments/${userId}`, {
        headers: {
          Authorization: `Bearer ${jwtToken}`
        }
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['userRegionAssignments']);
      setSnackbar({
        open: true,
        message: 'User region assignment deleted successfully',
        severity: 'success'
      });
      setSelectedUser('');
      setSelectedRegions([]);
    },
    onError: (error) => {
      console.error('Error deleting assignment:', error);
      setSnackbar({
        open: true,
        message: 'Failed to delete user region assignment',
        severity: 'error'
      });
    }
  });

  // Load user's regions when a user is selected
  useEffect(() => {
    if (selectedUser && assignments) {
      const userAssignment = assignments.find(a => a.userId === selectedUser);
      if (userAssignment) {
        setSelectedRegions(userAssignment.regions.map(r => r._id));
      } else {
        setSelectedRegions([]);
      }
    }
  }, [selectedUser, assignments]);

  const handleUserChange = (event) => {
    setSelectedUser(event.target.value);
  };

  const handleRegionChange = (event) => {
    const {
      target: { value },
    } = event;
    setSelectedRegions(
      typeof value === 'string' ? value.split(',') : value,
    );
  };

  const handleSave = () => {
    if (!selectedUser) {
      setSnackbar({
        open: true,
        message: 'Please select a user',
        severity: 'warning'
      });
      return;
    }

    const selectedUserObj = users.find(u => u.id === selectedUser);
    if (!selectedUserObj) return;

    createOrUpdateMutation.mutate({
      userId: selectedUser,
      userName: selectedUserObj.name,
      email: selectedUserObj.email,
      regions: selectedRegions
    });
  };

  const handleDelete = () => {
    if (!selectedUser) {
      setSnackbar({
        open: true,
        message: 'Please select a user',
        severity: 'warning'
      });
      return;
    }

    deleteMutation.mutate(selectedUser);
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  if (loadingUsers || loadingRegions || loadingAssignments) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="100vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box p={3}>
      <DashboardHeader
        title="User Region Assignments"
        description="Assign regions to users for proposal processing"
      />

      <Paper elevation={3} sx={{ p: 3, mt: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel id="user-select-label">Select User</InputLabel>
              <Select
                labelId="user-select-label"
                id="user-select"
                value={selectedUser}
                onChange={handleUserChange}
                label="Select User"
              >
                {users?.map((user) => (
                  <MenuItem key={user.id} value={user.id}>
                    {`${user.name} (${user.email})`}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel id="region-select-label">Assigned Regions</InputLabel>
              <Select
                labelId="region-select-label"
                id="region-select"
                multiple
                value={selectedRegions}
                onChange={handleRegionChange}
                input={<OutlinedInput label="Assigned Regions" />}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => {
                      const region = regions.find(r => r._id === value);
                      return (
                        <Chip key={value} label={region?.Region || value} />
                      );
                    })}
                  </Box>
                )}
                MenuProps={MenuProps}
              >
                {Array.isArray(regions) && regions.map((region) => (
                  <MenuItem key={region._id} value={region._id}>
                    <Checkbox checked={selectedRegions.indexOf(region._id) > -1} />
                    <ListItemText primary={region.Region} />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <Box display="flex" justifyContent="flex-end" gap={2} mt={2}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleSave}
                disabled={createOrUpdateMutation.isLoading}
              >
                {createOrUpdateMutation.isLoading ? <CircularProgress size={24} /> : 'Save Assignment'}
              </Button>
              <Button
                variant="outlined"
                color="error"
                onClick={handleDelete}
                disabled={deleteMutation.isLoading || !selectedUser}
              >
                {deleteMutation.isLoading ? <CircularProgress size={24} /> : 'Remove Assignment'}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      <Paper elevation={3} sx={{ p: 3, mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          Current Assignments
        </Typography>
        
        {assignments?.length === 0 ? (
          <Typography>No assignments found</Typography>
        ) : (
          <Box sx={{ mt: 2 }}>
            {assignments?.map((assignment) => (
              <Paper key={assignment._id} sx={{ p: 2, mb: 2, bgcolor: '#f5f5f5' }}>
                <Typography variant="subtitle1" fontWeight="bold">
                  {assignment.userName}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {assignment.email}
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                  {Array.isArray(assignment.regions) && assignment.regions.map((region) => (
                    <Chip 
                      key={region._id} 
                      label={region.Region} 
                      size="small" 
                      color="primary" 
                      variant="outlined" 
                    />
                  ))}
                </Box>
              </Paper>
            ))}
          </Box>
        )}
      </Paper>

      <Paper elevation={3} sx={{ p: 3, mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          Add User
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              label="User ID"
              value={newUser.id}
              onChange={(e) => setNewUser({...newUser, id: e.target.value})}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              label="Name"
              value={newUser.name}
              onChange={(e) => setNewUser({...newUser, name: e.target.value})}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="Email"
              value={newUser.email}
              onChange={(e) => setNewUser({...newUser, email: e.target.value})}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="contained"
              onClick={handleAddUser}
              sx={{ height: '100%' }}
            >
              Add User
            </Button>
          </Grid>
        </Grid>
      </Paper>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default UserRegionAssignmentPage;







