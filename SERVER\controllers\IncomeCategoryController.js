const IncomeCat = require("../models/IncomeCategory");
const IncomeSubcategory = require("../models/IncomeSubcategory");
const { isValidObjectId } = require("mongoose");

exports.getAllCategories1 = async (req, res) => {
  try {
    const categories = await IncomeCat.find().sort({ createdAt: -1 });
    res.status(200).json(categories);
  } catch (error) {
    res.status(500).json({ error: "Server error" });
  }
};

exports.getAllCategories = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      orderBy = "createdAt",
      order = "asc",
      incomeCategoryName,
      incomeSubcategoryName,
      description,
    } = req.query;

    let query = {};

    if (search) {
      query.$or = [
        { incomeCategoryName: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
      ];
    }

    if (incomeCategoryName) {
      query.incomeCategoryName = { $regex: incomeCategoryName, $options: "i" };
    }
    if (description) {
      query.description = { $regex: description, $options: "i" };
    }

    const sortOrder = order.toLowerCase() === "desc" ? -1 : 1;
    const sortQuery = { [orderBy]: sortOrder };

    const pageNum = Math.max(1, Number(page));
    const limitNum = Math.max(1, Number(limit));
    const skip = (pageNum - 1) * limitNum;

    const categories = await IncomeCat.find(query)
      .skip(skip)
      .limit(limitNum)
      .sort(sortQuery);

    const populatedCategories = categories.map(category => {
      return category.toObject();
    });

    const totalRecords = await IncomeCat.countDocuments(query);

    return res.json({
      categories: populatedCategories,
      totalPages: Math.ceil(totalRecords / limitNum),
      currentPage: pageNum,
      totalRecords,
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: "Failed to fetch income categories." });
  }
};

exports.getSubcategories = async (req, res) => {
  try {
    const items = await IncomeSubcategory.distinct("name");
    res.status(200).json({incomeSubcategoryName: items});
  } catch (error) {
    res.status(500).json({ error: "Server error" });
  }
};

exports.addIncomeCategory = async (req, res) => {
  try {
    console.log("Received request to add income category:", req.body);
    const { name, incomeCategoryName, description, incomeSubcategoryName } = req.body;
    const categoryName = (incomeCategoryName || name)?.trim();

    console.log("Extracted fields:", { categoryName, description, incomeSubcategoryName });

    if (!categoryName) {
      console.log("Validation failed: Income category name is required");
      return res.status(400).json({ error: "Income category name is required." });
    }

    const existingCategory = await IncomeCat.findOne({
      incomeCategoryName: categoryName
    });

    if (existingCategory) {
      console.log("Validation failed: Category already exists");
      return res.status(400).json({ error: "A category with this name already exists." });
    }

    const newIncomeCategory = new IncomeCat({
      incomeCategoryName: categoryName,
      incomeSubcategoryName: incomeSubcategoryName || [],
      description: description || ""
    });

    console.log("Saving new income category:", newIncomeCategory);
    await newIncomeCategory.save();

    console.log("Income category saved successfully");
    return res.status(201).json({
      message: "Income category created successfully.",
      incomeCategory: newIncomeCategory,
    });
  } catch (error) {
    console.error("Error in addIncomeCategory:", error);
    return res.status(500).json({ error: "Failed to create income category." });
  }
};

exports.editIncomeCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { incomeCategoryName, description, incomeSubcategoryName } = req.body;

    // Validate ID and required fields
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ error: "Invalid category ID" });
    }
    if (!incomeCategoryName) {
      return res.status(400).json({ error: "Category name is required" });
    }

    // Check for duplicate name
    const duplicate = await IncomeCat.findOne({
      incomeCategoryName,
      _id: { $ne: id }
    });
    if (duplicate) {
      return res.status(400).json({ error: "A category with this name already exists" });
    }

    // Prepare update data
    const updateData = {
      incomeCategoryName,
      name: incomeCategoryName,
      description: description || "",
      incomeSubcategoryName: Array.isArray(incomeSubcategoryName) ? incomeSubcategoryName : []
    };

    // Update the category
    const updatedCategory = await IncomeCat.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );

    if (!updatedCategory) {
      return res.status(404).json({ error: "Category not found" });
    }

    res.status(200).json({
      message: "Income category updated successfully",
      category: updatedCategory
    });
  } catch (error) {
    console.error("Error updating income category:", error);
    res.status(500).json({ error: "Failed to update income category" });
  }
};

exports.deleteIncomeCategory = async (req, res) => {
  try {
    const { id } = req.params;
    if (!isValidObjectId(id)) {
      return res.status(400).json({ error: "Invalid category ID" });
    }
    const category = await IncomeCat.findByIdAndDelete(id);
    if (!category) {
      return res.status(404).json({ error: "Category not found" });
    }
    res.status(200).json({ message: "Category deleted" });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: "Failed to delete income category." });
  }
};

exports.getCategoryById = async (req, res) => {
  try {
    const { id } = req.params;
    if (!isValidObjectId(id)) {
      return res.status(400).json({ error: "Invalid category ID" });
    }
    const category = await IncomeCat.findById(id);
    if (!category) {
      return res.status(404).json({ error: "Category not found" });
    }
    res.status(200).json(category);
  } catch (error) {
    res.status(500).json({ error: "Server error" });
  }
};

exports.createCategory = async (req, res) => {
  try {
    console.log("Request body:", req.body);
    const { name, incomeCategoryName, description, incomeSubcategoryName } = req.body;
    const categoryName = incomeCategoryName || name;

    console.log("Extracted fields:", categoryName, incomeSubcategoryName, description);

    if (!categoryName) {
      console.log("Validation failed: Name is required");
      return res.status(400).json({ error: "Name is required" });
    }

    const existingCategory = await IncomeCat.findOne({
      incomeCategoryName: categoryName
    });

    if (existingCategory) {
      console.log("Validation failed: Category name already exists");
      return res.status(400).json({ error: "A category with this name already exists." });
    }

    const newCategory = new IncomeCat({
      incomeCategoryName: categoryName,
      name: categoryName, // Add this line to set the name field
      incomeSubcategoryName: incomeSubcategoryName || [],
      description: description || ""
    });

    console.log("New category object:", newCategory);
    await newCategory.save();

    console.log("Category saved successfully");
    return res.status(201).json({
      message: "Category created successfully.",
      category: newCategory,
    });
  } catch (error) {
    console.error("Error in createCategory:", error);
    return res.status(500).json({ error: "Failed to create category." });
  }
};

exports.updateCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;
    if (!isValidObjectId(id)) {
      return res.status(400).json({ error: "Invalid category ID" });
    }
    if (!name) {
      return res.status(400).json({ error: "Name is required" });
    }
    const category = await IncomeCat.findByIdAndUpdate(
      id,
      { name, description },
      { new: true }
    );
    if (!category) {
      return res.status(404).json({ error: "Category not found" });
    }
    res.status(200).json(category);
  } catch (error) {
    res.status(500).json({ error: "Server error" });
  }
};

exports.deleteCategory = async (req, res) => {
  try {
    const { id } = req.params;
    if (!isValidObjectId(id)) {
      return res.status(400).json({ error: "Invalid category ID" });
    }
    const category = await IncomeCat.findByIdAndDelete(id);
    if (!category) {
      return res.status(404).json({ error: "Category not found" });
    }
    res.status(200).json({ message: "Category deleted" });
  } catch (error) {
    res.status(500).json({ error: "Server error" });
  }
};

// Get all income categories
exports.getAllIncomeCategories = async (req, res) => {
  try {
    const incomeCategories = await IncomeCategory.find()
      .sort({ incomeCategoryName: 1 });
    
    return res.status(200).json({
      categories: incomeCategories,
      success: true
    });
  } catch (error) {
    console.error("Error in getAllIncomeCategories:", error);
    return res.status(500).json({ 
      error: "Failed to fetch income categories",
      success: false
    });
  }
};
