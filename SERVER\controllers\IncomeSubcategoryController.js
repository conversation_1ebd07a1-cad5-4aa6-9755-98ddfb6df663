const IncomeSubcategory = require("../models/IncomeSubcategory");
const { textFilter } = require("../utils/controller_get_process");

// Get all income subcategories
exports.getAllIncomeSubcategories = async (req, res) => {
  try {
    const incomeSubcategories = await IncomeSubcategory.find()
      .sort({ incomeSubcategoryName: 1 });
    
    return res.status(200).json({
      incomeSubcategories,
      success: true
    });
  } catch (error) {
    console.error("Error in getAllIncomeSubcategories:", error);
    return res.status(500).json({ 
      error: "Failed to fetch income subcategories",
      success: false
    });
  }
};

// Create a new income subcategory
exports.addIncomeSubcategory = async (req, res) => {
  try {
    console.log("Request body:", req.body);
    const { incomeSubcategoryName } = req.body;
    
    console.log("Extracted field:", incomeSubcategoryName);

    if (!incomeSubcategoryName) {
      console.log("Validation failed: Income subcategory name is required");
      return res.status(400).json({ error: "Income subcategory name is required." });
    }

    // Check if a subcategory with this name already exists
    const existingSubcategory = await IncomeSubcategory.findOne({
      $or: [
        { incomeSubcategoryName: incomeSubcategoryName },
        { name: incomeSubcategoryName }
      ]
    });

    if (existingSubcategory) {
      return res.status(400).json({ error: "A subcategory with this name already exists." });
    }

    const newIncomeSubcategory = new IncomeSubcategory({
      incomeSubcategoryName,
      name: incomeSubcategoryName // Set both fields to ensure consistency
    });

    console.log("New subcategory object:", newIncomeSubcategory);
    await newIncomeSubcategory.save();
    
    console.log("Subcategory saved successfully");
    return res.status(201).json({
      message: "Income subcategory created successfully.",
      incomeSubcategory: newIncomeSubcategory,
    });
  } catch (error) {
    console.error("Error in addIncomeSubcategory:", error);
    return res.status(500).json({ error: "Failed to create income subcategory." });
  }
};

// Update an existing income subcategory
exports.editIncomeSubcategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { incomeSubcategoryName } = req.body;

    if (!incomeSubcategoryName) {
      return res.status(400).json({ error: "Income subcategory name is required." });
    }

    // Check if another subcategory with this name already exists
    const existingSubcategory = await IncomeSubcategory.findOne({
      $or: [
        { incomeSubcategoryName: incomeSubcategoryName },
        { name: incomeSubcategoryName }
      ],
      _id: { $ne: id } // Exclude the current subcategory
    });

    if (existingSubcategory) {
      return res.status(400).json({ error: "Another subcategory with this name already exists." });
    }

    const updatedIncomeSubcategory = await IncomeSubcategory.findByIdAndUpdate(
      id,
      { 
        incomeSubcategoryName,
        name: incomeSubcategoryName // Update both fields
      },
      { new: true, runValidators: true }
    );

    if (!updatedIncomeSubcategory) {
      return res.status(404).json({ error: "Income subcategory not found." });
    }

    return res.json({
      message: "Income subcategory updated successfully.",
      incomeSubcategory: updatedIncomeSubcategory,
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: "Failed to update income subcategory." });
  }
};

// Delete an income subcategory
exports.deleteIncomeSubcategory = async (req, res) => {
  try {
    const { id } = req.params;

    const deletedIncomeSubcategory = await IncomeSubcategory.findByIdAndDelete(id);
    if (!deletedIncomeSubcategory) {
      return res.status(404).json({ error: "Income subcategory not found." });
    }

    return res.json({ message: "Income subcategory deleted successfully." });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: "Failed to delete income subcategory." });
  }
};
