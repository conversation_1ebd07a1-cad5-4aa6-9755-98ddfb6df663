const UserRegionAssignment = require('../models/UserRegionAssignment');
const Region = require('../models/Region');

// Get all user-region assignments
const getAllUserRegionAssignments = async (req, res) => {
  try {
    const assignments = await UserRegionAssignment.find().populate('regions');
    return res.status(200).json(assignments);
  } catch (error) {
    console.error('Error fetching user-region assignments:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Get assignments for a specific user
const getUserRegionAssignments = async (req, res) => {
  try {
    const { userId } = req.params;
    const assignments = await UserRegionAssignment.findOne({ userId }).populate('regions');
    
    if (!assignments) {
      return res.status(200).json({ userId, regions: [] });
    }
    
    return res.status(200).json(assignments);
  } catch (error) {
    console.error('Error fetching user region assignments:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Get current user's region assignments
const getCurrentUserRegionAssignments = async (req, res) => {
  try {
    const userId = req.user.id;
    const assignments = await UserRegionAssignment.findOne({ userId }).populate('regions');
    
    if (!assignments) {
      return res.status(200).json({ userId, regions: [] });
    }
    
    return res.status(200).json(assignments);
  } catch (error) {
    console.error('Error fetching current user region assignments:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Create or update user-region assignment
const createOrUpdateUserRegionAssignment = async (req, res) => {
  try {
    const { userId, userName, email, regions } = req.body;
    
    if (!userId || !userName || !email || !regions) {
      return res.status(400).json({ message: 'Missing required fields' });
    }
    
    // Find existing assignment or create new one
    let assignment = await UserRegionAssignment.findOne({ userId });
    
    if (assignment) {
      // Update existing assignment
      assignment.regions = regions;
      assignment.userName = userName;
      assignment.email = email;
      assignment.updatedAt = Date.now();
      await assignment.save();
    } else {
      // Create new assignment
      assignment = await UserRegionAssignment.create({
        userId,
        userName,
        email,
        regions,
      });
    }
    
    // Populate regions before returning
    const populatedAssignment = await UserRegionAssignment.findById(assignment._id).populate('regions');
    
    return res.status(200).json(populatedAssignment);
  } catch (error) {
    console.error('Error creating/updating user-region assignment:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Delete user-region assignment
const deleteUserRegionAssignment = async (req, res) => {
  try {
    const { userId } = req.params;
    
    const result = await UserRegionAssignment.findOneAndDelete({ userId });
    
    if (!result) {
      return res.status(404).json({ message: 'Assignment not found' });
    }
    
    return res.status(200).json({ message: 'Assignment deleted successfully' });
  } catch (error) {
    console.error('Error deleting user-region assignment:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Check if user has access to a region
const checkUserRegionAccess = async (userId, region) => {
  try {
    // If no region specified, allow access
    if (!region) return true;
    
    // Find user's region assignments
    const userAssignment = await UserRegionAssignment.findOne({ userId }).populate('regions');
    
    // If no assignments found, check if any assignments exist in the system
    if (!userAssignment) {
      const anyAssignments = await UserRegionAssignment.countDocuments();
      // If no assignments exist in the system, allow access (system not using region restrictions yet)
      if (anyAssignments === 0) return true;
      // Otherwise, deny access
      return false;
    }
    
    // Check if user is assigned to this region
    return userAssignment.regions.some(r => r.Region === region);
  } catch (error) {
    console.error('Error checking user region access:', error);
    return false;
  }
};

module.exports = {
  getAllUserRegionAssignments,
  getUserRegionAssignments,
  getCurrentUserRegionAssignments,
  createOrUpdateUserRegionAssignment,
  deleteUserRegionAssignment,
  checkUserRegionAccess
};




