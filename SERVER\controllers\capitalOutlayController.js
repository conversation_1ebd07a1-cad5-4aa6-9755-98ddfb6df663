const mongoose = require("mongoose");
const CapitalOutlay = require("../models/CapitalOutlay");
const ChartOfAccounts = require('../models/chartOfAccounts');
const Category = require("../models/Category");
const Settings = require("../models/Settings");


// Helper function to get the active fiscal year
const getActiveFiscalYear = async () => {
  const activeSettings = await Settings.findOne({ isActive: true });
  if (!activeSettings) {
    throw new Error("Active settings not found");
  }
  return activeSettings.fiscalYear;
};

// Get all capital outlays with search & pagination
exports.getAllCapitalOutlays = async (req, res) => {
  try {
    const { page = 1, search, category, orderBy, order = "asc" } = req.query;

    let query = {};

    // Add fiscal year to the query
    try {
      query.fiscalYear = await getActiveFiscalYear();
    } catch (error) {
      return res.status(400).json({ error: error.message });
    }

    if (search) {
      query.title = { $regex: search, $options: "i" };
    }

    if (category && mongoose.Types.ObjectId.isValid(category)) {
      query.category = new mongoose.Types.ObjectId(category);
    } else {
      query.category = { $ne: null };
    }

    const sortQuery = { [orderBy || "createdAt"]: order === "desc" ? -1 : 1 };

    const [capitalOutlays, totalRecords] = await Promise.all([
      CapitalOutlay.find(query)
        .populate({
          path: "category",
          select: "categoryName _id",
          options: { strictPopulate: false },
        })
        .sort(sortQuery)
        .skip((page - 1) * 100) // Adjust the skip value if needed
        .exec(),
      CapitalOutlay.countDocuments(query),
    ]);

    // Format the response to ensure category is properly extracted
    const formattedOutlays = capitalOutlays.map((outlay) => {
      const outlayObj = outlay.toObject();
      
      // Ensure category is properly formatted
      let categoryData = null;
      if (outlay.category) {
        if (typeof outlay.category === 'object') {
          categoryData = {
            _id: outlay.category._id.toString(),
            categoryName: outlay.category.categoryName,
          };
        } else {
          categoryData = {
            _id: outlay.category.toString(),
            categoryName: outlay.category.toString(),
          };
        }
      }
      
      return {
        ...outlayObj,
        category: categoryData,
      };
    });

    res.status(200).json({
      capitalOutlays: formattedOutlays,
      totalPages: Math.ceil(totalRecords / 100), // Adjust the totalPages calculation if needed
      currentPage: parseInt(page),
      totalRecords,
    });
  } catch (error) {
    console.error("Error fetching capital outlays:", error);
    res.status(500).json({ error: "Failed to retrieve capital outlays." });
  }
};

// Add a new capital outlay
// Add a new capital outlay
// Add a new capital outlay
exports.addCapitalOutlay = async (req, res) => {
  try {
    const {
      particulars,
      cost,
      income, // <-- Add this line
      subsidy, // Added subsidy field
      category,
      processBy,
      region,
      processDate,
      fiscalYear,
      sublineItem,
      accountingTitle,
      uacsCode,
    } = req.body;

    // ─── VALIDATION: lahat ng fields ay required ───
    if (
      cost == null ||
      !category ||
      !sublineItem ||
      !accountingTitle ||
      !uacsCode ||
      !region // Ensure region is required
    ) {
      return res
        .status(400)
        .json({
          error:
            "Missing fields: cost, category, sublineItem, accountingTitle, uacsCode, and region are all required.",
        });
    }

    // Fetch active settings
    const activeSettings = await Settings.findOne({ isActive: true });
    if (!activeSettings) {
      return res
        .status(400)
        .json({ error: "Active settings not found" });
    }

    const newOutlay = new CapitalOutlay({
      particulars,
      cost,
      income: income || 0, // <-- Add this line
      subsidy: subsidy || 0, // Added subsidy field with default value
      category,
      processBy,
      region,
      processDate,
      fiscalYear,
      budgetType: activeSettings.budgetType,
      sublineItem,
      accountingTitle,
      uacsCode,
    });

    await newOutlay.save();
    return res
      .status(201)
      .json({ message: "Capital outlay added successfully" });
  } catch (error) {
    console.error("Error adding capital outlay:", error);
    return res
      .status(500)
      .json({ error: "Internal server error" });
  }
};

// Edit an existing capital outlay
exports.editCapitalOutlay = async (req, res) => {
  try {
    const { id } = req.params;
    const { title, particulars, cost, income, subsidy, category, processBy, region, processDate, fiscalYear, status, sublineItem, accountingTitle, uacsCode } = req.body;

    console.log("Received request to update capital outlay");
    console.log("Received ID:", id);
    console.log("Request Body:", req.body);

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      console.error("Invalid capital outlay ID:", id);
      return res.status(400).json({ error: "Invalid capital outlay ID." });
    }

    // Validate category ID format if provided
    if (category && !mongoose.Types.ObjectId.isValid(category)) {
      console.error("Invalid category ID:", category);
      return res.status(400).json({ error: "Invalid category ID." });
    }

    // Fetch fiscalYear and budgetType from active settings
    const activeSettings = await Settings.findOne({ isActive: true });
    if (!activeSettings) {
      return res.status(400).json({ error: "Active settings not found" });
    }
    const budgetType = activeSettings.budgetType;

    // Check if the capital outlay exists
    const existingOutlay = await CapitalOutlay.findById(id);
    if (!existingOutlay) {
      console.error("Capital outlay not found for ID:", id);
      return res.status(404).json({ error: "Capital outlay not found." });
    }

    // Update capital outlay
    const updatedCapitalOutlay = await CapitalOutlay.findByIdAndUpdate(
      id,
      {
        title,
        particulars,
        cost,
        income: income || 0, // <-- Add this line
        subsidy: subsidy || 0, // Added subsidy field with default value
        category,
        processBy,
        region,
        processDate,
        fiscalYear,
        budgetType, // Include budgetType
        status,
        sublineItem,
        accountingTitle,
        uacsCode,
      },
      { new: true, runValidators: true }
    ).populate("category"); // Ensure updated category details are returned

    console.log("Updated capital outlay:", updatedCapitalOutlay);

    return res.status(200).json({
      message: "Capital outlay updated successfully.",
      capitalOutlay: updatedCapitalOutlay,
    });
  } catch (error) {
    console.error("Error updating capital outlay:", error);
    return res.status(500).json({ error: "Failed to update capital outlay." });
  }
};

// Delete a capital outlay
exports.deleteCapitalOutlay = async (req, res) => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ error: "Invalid capital outlay ID." });
    }

    const deletedCapitalOutlay = await CapitalOutlay.findByIdAndDelete(id);

    if (!deletedCapitalOutlay) {
      return res.status(404).json({ error: "Capital outlay not found." });
    }

    res.status(200).json({ message: "Capital outlay deleted successfully." });
  } catch (error) {
    console.error("Error deleting capital outlay:", error);
    res.status(500).json({ error: "Failed to delete capital outlay." });
  }
};

// Fetch all capital outlay list
exports.getCapitalOutlayList = async (req, res) => {
  try {
    const capitalOutlays = await CapitalOutlay.find();
    res.status(200).json(capitalOutlays);
  } catch (error) {
    console.error("Error fetching capital outlays:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

exports.getSublineItems = async (req, res) => {
  try {
    const { category } = req.query;
    console.log("Fetching subline items for category:", category);

    // Fetch the category document dynamically
    const categoryDoc = await Category.findOne({ categoryName: category });

    if (!categoryDoc) {
      return res.status(404).json({ error: "Category not found." });
    }

    // Get sublineItems from the category document
    const sublineItemList = categoryDoc.sublineItems || [];

    // Fetch matching sublineItems from ChartOfAccounts (if needed)
    const fetchedSublineItems = await ChartOfAccounts.find({
      sublineItem: { $in: sublineItemList },
    }).distinct("sublineItem");

    console.log("Fetched subline items:", fetchedSublineItems);
    res.status(200).json({ sublineItems: fetchedSublineItems });
  } catch (error) {
    console.error("Error fetching subline items:", error);
    res.status(500).json({ error: "Failed to retrieve subline items." });
  }
};

// Fetch accounting titles based on subline item
exports.getAccountingTitles = async (req, res) => {
  try {
    const { sublineItem } = req.query;
    console.log("Fetching accounting titles for subline item:", sublineItem); // Debugging

    const accountingTitles = await ChartOfAccounts.find({ sublineItem }).select("accountingTitle uacsCode");

    console.log("Fetched accounting titles:", accountingTitles); // Debugging
    res.status(200).json({ accountingTitles });
  } catch (error) {
    console.error("Error fetching accounting titles:", error);
    res.status(500).json({ error: "Failed to retrieve accounting titles." });
  }
};

// deleteCapitalOutlay all records
exports.deleteAllCapitalOutlays = async (req, res) => {
  try {
    await CapitalOutlay.deleteMany({}); // Delete all records
    res.status(200).json({ message: "All capital outlays deleted successfully." });
  } catch (error) {
    console.error("Error deleting capital outlays:", error);  // Debugging
    res.status(500).json({ error: "Failed to delete capital outlays." });
  }
};
