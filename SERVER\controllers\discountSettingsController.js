const DiscountSettings = require("../models/DiscountSettings");

// Get discount settings
exports.getDiscountSettings = async (req, res) => {
  try {
    const { fiscalYear, budgetType } = req.query;
    
    // Use default values if not provided
    const year = fiscalYear || new Date().getFullYear().toString();
    const budget = budgetType || "REGULAR";
    
    console.log(`Fetching discount settings for FY: ${year}, Budget: ${budget}`);
    
    // Find settings for this fiscal year and budget type
    let settings = await DiscountSettings.findOne({ 
      fiscalYear: year, 
      budgetType: budget,
      name: "ISF_DISCOUNT"
    });
    
    // If no settings exist, create default settings
    if (!settings) {
      console.log("No settings found, creating defaults");
      settings = await DiscountSettings.create({
        name: "ISF_DISCOUNT",
        discountType: "percentage",
        discountValue: 10,
        applyDiscount: true,
        fiscalYear: year,
        budgetType: budget,
        processBy: req.user?.username || "system",
        processDate: new Date()
      });
    }
    
    console.log("Returning settings:", settings);
    res.status(200).json(settings);
  } catch (error) {
    console.error("Error getting discount settings:", error);
    res.status(500).json({ 
      message: "Failed to get discount settings", 
      error: error.message
    });
  }
};

// Update discount settings
exports.updateDiscountSettings = async (req, res) => {
  try {
    const { fiscalYear, budgetType, discountType, discountValue, applyDiscount } = req.body;
    
    if (!fiscalYear || !budgetType) {
      return res.status(400).json({ 
        message: "Fiscal year and budget type are required" 
      });
    }
    
    console.log(`Updating discount settings for FY: ${fiscalYear}, Budget: ${budgetType}`);
    console.log("New values:", { discountType, discountValue, applyDiscount });
    
    // Find and update settings, or create if they don't exist
    const settings = await DiscountSettings.findOneAndUpdate(
      { 
        fiscalYear, 
        budgetType,
        name: "ISF_DISCOUNT"
      },
      {
        $set: {
          discountType,
          discountValue,
          applyDiscount,
          processBy: req.user?.username || "system",
          processDate: new Date()
        }
      },
      { 
        new: true, 
        upsert: true 
      }
    );
    
    console.log("Updated settings:", settings);
    res.status(200).json(settings);
  } catch (error) {
    console.error("Error updating discount settings:", error);
    res.status(500).json({ 
      message: "Failed to update discount settings", 
      error: error.message
    });
  }
};

