// controllers/loyaltyPayController.js

const LoyaltyPay        = require("../models/loyaltyPay");
const Settings          = require("../models/Settings");
const PersonnelServices = require("../models/personnelServices"); // ← Sync target :contentReference[oaicite:0]{index=0}&#8203;:contentReference[oaicite:1]{index=1}

/**
 * Helper: compute total loyalty pay for an employee in a fiscal year
 */
async function computeTotalLoyalty(employeeFullName, fiscalYear) {
  const result = await LoyaltyPay.aggregate([
    { $match: { employeeFullName, fiscalYear } },
    { $group: { _id: null, totalAmount: { $sum: "$amount" } } }
  ]);
  return result[0]?.totalAmount || 0;
}

/**
 * Create a new Loyalty Pay record,
 * then immediately sync combined loyaltyAward in PersonnelServices.
 */
exports.createLoyaltyPay = async (req, res) => {
  try {
    const {
      employeeNumber,
      employeeFullName,
      positionTitle,
      department,
      division,
      region,
      yearsInService,
      fiscalYear, // optional
      budgetType, // optional
      processBy,
      processDate,
    } = req.body;

    // 1) Get settings
    const settings = fiscalYear
      ? await Settings.findOne({ fiscalYear, isActive: true }).lean()
      : await Settings.findOne({ isActive: true }).lean();

    if (!settings?.loyaltyPay) {
      return res.status(400).json({
        message: "Active settings with loyalty pay not found."
      });
    }

    // 2) Compute amount
    const { baseAmount, succeedingAmount } = settings.loyaltyPay;
    const yrs = Number(yearsInService);
    const validYears = [10, 15, 20, 25, 30, 35, 40, 45, 50];
    const amount = validYears.includes(yrs)
      ? (yrs === 10 ? baseAmount : succeedingAmount)
      : 0;

    const usedFY = fiscalYear || settings.fiscalYear;

    // 3) Save LoyaltyPay record
    const loyalty = new LoyaltyPay({
      employeeNumber,
      employeeFullName,
      positionTitle,
      department,
      division,
      region,
      yearsInService: yrs,
      amount,
      fiscalYear: usedFY,
      budgetType: budgetType || settings.budgetType,
      processBy,
      processDate,
    });
    await loyalty.save();

    // 4) Sync combined loyaltyAward to PersonnelServices
    const totalLoyalty = await computeTotalLoyalty(employeeFullName, usedFY);
    const person = await PersonnelServices.findOne({
      employeeFullName,
      fiscalYear: usedFY,
    });
    if (person) {
      person.loyaltyAward = totalLoyalty;
      await person.save();
    }

    return res.status(201).json({
      message: "Loyalty Pay record saved and PersonnelServices updated",
      data: loyalty,
    });
  } catch (err) {
    console.error("❌ Error in createLoyaltyPay:", err);
    return res.status(500).json({ message: "Server error", error: err.message });
  }
};

/**
 * Get all Loyalty Pay records
 * (original logic unchanged)
 */
exports.getAllLoyaltyPays = async (req, res) => {
  try {
    const { fiscalYear } = req.query;
    let query = {};
    if (fiscalYear) {
      query.fiscalYear = fiscalYear;
    } else {
      const active = await Settings.findOne({ isActive: true }).lean();
      if (active) query.fiscalYear = active.fiscalYear;
    }
    const records = await LoyaltyPay.find(query).lean().sort({ createdAt: -1 });
    return res.status(200).json({ data: records });
  } catch (err) {
    console.error("❌ Error in getAllLoyaltyPays:", err);
    return res.status(500).json({ message: "Server error", error: err.message });
  }
};

/**
 * Update a Loyalty Pay record,
 * then recompute & sync combined loyaltyAward to PersonnelServices.
 */
exports.updateLoyaltyPay = async (req, res) => {
  try {
    const { id } = req.params;
    const { yearsInService, fiscalYear } = req.body;

    // 1) Get settings
    const settings = fiscalYear
      ? await Settings.findOne({ fiscalYear, isActive: true }).lean()
      : await Settings.findOne({ isActive: true }).lean();
    if (!settings?.loyaltyPay) {
      return res.status(400).json({
        message: "Active settings with loyalty pay not found."
      });
    }

    // 2) Compute new amount
    const { baseAmount, succeedingAmount } = settings.loyaltyPay;
    const yrs = Number(yearsInService);
    const validYears = [10, 15, 20, 25, 30, 35, 40, 45, 50];
    const amount = validYears.includes(yrs)
      ? (yrs === 10 ? baseAmount : succeedingAmount)
      : 0;

    const usedFY = fiscalYear || settings.fiscalYear;

    // 3) Update record
    const updated = await LoyaltyPay.findByIdAndUpdate(
      id,
      { ...req.body, yearsInService: yrs, amount, fiscalYear: usedFY },
      { new: true }
    );
    if (!updated) return res.status(404).json({ message: "Record not found." });

    // 4) Sync to PersonnelServices
    const totalLoyalty = await computeTotalLoyalty(updated.employeeFullName, usedFY);
    const person = await PersonnelServices.findOne({
      employeeFullName: updated.employeeFullName,
      fiscalYear:     usedFY,
    });
    if (person) {
      person.loyaltyAward = totalLoyalty;
      await person.save();
    }

    return res.status(200).json(updated);
  } catch (err) {
    console.error("❌ Error in updateLoyaltyPay:", err);
    return res.status(500).json({ message: "Server error", error: err.message });
  }
};

/**
 * Delete a Loyalty Pay record,
 * then recompute & sync combined loyaltyAward to PersonnelServices.
 */
exports.deleteLoyaltyPay = async (req, res) => {
  try {
    const { id } = req.params;
    const toDel = await LoyaltyPay.findById(id);
    if (!toDel) return res.status(404).json({ message: "Record not found." });

    await LoyaltyPay.findByIdAndDelete(id);

    const totalLoyalty = await computeTotalLoyalty(toDel.employeeNumber, toDel.fiscalYear);
    const person = await PersonnelServices.findOne({
      employeeNumber: toDel.employeeNumber,
      fiscalYear:     toDel.fiscalYear,
    });
    if (person) {
      person.loyaltyAward = totalLoyalty;
      person.Total = Object.values(person.toObject()).reduce(
        (sum, v) => (typeof v === "number" ? sum + v : sum),
        0
      );
      await person.save();
    }

    return res.status(200).json({ message: "Deleted and PersonnelServices updated." });
  } catch (err) {
    console.error("❌ Error in deleteLoyaltyPay:", err);
    return res.status(500).json({ message: "Server error", error: err.message });
  }
};

/**
 * Get sum of Loyalty Pay amounts for active fiscal year
 * (original logic unchanged)
 */
exports.getSumOfLoyaltyPay = async (req, res) => {
  try {
    const active = await Settings.findOne({ isActive: true }).lean();
    if (!active) {
      return res.status(400).json({ message: "Active settings not found." });
    }
    const fiscalYear = active.fiscalYear;
    const result = await LoyaltyPay.aggregate([
      { $match: { fiscalYear } },
      { $group: { _id: null, totalAmount: { $sum: "$amount" } } },
    ]);
    const totalAmount = result[0]?.totalAmount || 0;
    return res.status(200).json({ totalAmount });
  } catch (err) {
    console.error("❌ Error in getSumOfLoyaltyPay:", err);
    return res
      .status(500)
      .json({ error: "Failed to calculate total loyalty pay amount.", message: err.message });
  }
};
