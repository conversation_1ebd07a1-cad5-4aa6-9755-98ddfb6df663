// Add this new method to your existing controller

exports.getAllUserProposals = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Fetch all personnel services data
    const personnel = await Personnel.find({ 
      processBy: userId,
      // Include all statuses: Draft, Submitted, Returned
    }).sort({ updatedAt: -1 });
    
    // Fetch all MOOE data
    const mooe = await MOOE.find({ 
      processBy: userId,
      // Include all statuses
    }).sort({ updatedAt: -1 });
    
    // Fetch all capital outlay data
    const capitalOutlay = await CapitalOutlay.find({ 
      processBy: userId,
      // Include all statuses
    }).sort({ updatedAt: -1 });
    
    // Fetch all income data
    const income = await Income.find({ 
      processBy: userId,
      // Include all statuses
    }).sort({ updatedAt: -1 });
    
    res.status(200).json({
      personnel,
      mooe,
      capitalOutlay,
      income
    });
  } catch (error) {
    console.error("Error fetching all user proposals:", error);
    res.status(500).json({ message: "Failed to fetch proposals" });
  }
};

