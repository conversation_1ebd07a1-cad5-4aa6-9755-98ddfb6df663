const PersonnelServices = require("../models/PersonnelServices");
const EmployeeMaster = require("../models/EmployeeList");
const Settings = require("../models/Settings");
const {
  numberFilter,
  dateFilter,
  textFilter,
  booleanFilter,
  searchFilter,
} = require("../utils/controller_get_process");

// GET all personnel services with on-the-fly recalculation of computed fields
const getAllPersonnelServices = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      orderBy,
      order = "asc",
      employeeFullName,
      statusOfAppointment,
    } = req.query;

    const pageNum = Math.max(1, Number(page));
    const limitNum = Math.max(1, Number(limit));
    const skip = (pageNum - 1) * limitNum;

    // Kunin ang active settings para makuha ang fiscalYear at bagong mga rates
    const activeSettings = await Settings.findOne({ isActive: true });
    let query = {};

    if (activeSettings) {
      query.fiscalYear = activeSettings.fiscalYear;
    }

    if (search) {
      searchFilter(query, search, [
        "positionTitle",
        "employeeFullName",
        "department",
      ]);
    }

    textFilter(query, { employeeFullName });

    if (statusOfAppointment) {
      query.statusOfAppointment = statusOfAppointment;
    }

    const sortByField = orderBy || "createdAt";
    const sortOrder = order.toLowerCase() === "desc" ? -1 : 1;
    const sortQuery = { [sortByField]: sortOrder };

    let personnelServices = await PersonnelServices.find(query)
      .skip(skip)
      .limit(limitNum)
      .sort(sortQuery);
    const totalRecords = await PersonnelServices.countDocuments(query);

    // On-the-fly recalculation para sa dynamic computed fields
    if (activeSettings) {
      personnelServices = personnelServices.map((personnel) => {
        // Simplified calculation logic
        const recalculatedTotal = personnel.monthlySalary * 12;
        
        return {
          ...personnel.toObject(),
          Total: recalculatedTotal,
        };
      });
    }

    return res.json({
      personnelServices,
      totalPages: Math.ceil(totalRecords / limitNum),
      currentPage: pageNum,
      totalRecords,
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: "Something went wrong." });
  }
};

// Get all personnel services
const getAllPerServices = async (req, res) => {
  try {
    const personnelServices = await PersonnelServices.find();
    res.status(200).json(personnelServices);
  } catch (error) {
    console.error("Error fetching personnel services:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// Get personnel by parameters
const getPersonnelByParams = async (req, res) => {
  try {
    const { fiscalYear, budgetType, processBy, region, status } = req.query;
    
    console.log("Fetching personnel with params:", { fiscalYear, budgetType, processBy, region, status });
    
    const query = {};
    if (fiscalYear) query.fiscalYear = fiscalYear;
    if (budgetType) query.budgetType = budgetType;
    if (processBy) query.processBy = processBy;
    if (region) query.region = region;
    if (status) query.status = status;
    
    const personnel = await PersonnelServices.find(query).lean();
    
    console.log(`Found ${personnel.length} personnel records`);
    res.status(200).json(personnel);
  } catch (error) {
    console.error("Error fetching personnel by params:", error);
    res.status(500).json({ message: "Failed to fetch personnel data" });
  }
};

// getpersonnel hired before June 1988
const getPersonnelHiredBeforeJune1988 = async (req, res) => {
  try {
    const personnel = await PersonnelServices.find({
      DateOfAppointment: { $lt: new Date("1988-06-01") },
    });
    res.status(200).json(personnel);
  } catch (error) {
    console.error("Error fetching personnel:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

module.exports = {
  getAllPersonnelServices,
  getAllPerServices,
  getPersonnelByParams,
  PersonnelServices,  
  getPersonnelHiredBeforeJune1988,
};