const Settings = require("../models/Settings");
const PersonnelServices = require("../models/PersonnelServices");
const MOOEProposal = require("../models/mooeProposals");
const CapitalOutlay = require("../models/CapitalOutlay");
const Category = require("../models/Category");

const getConsolidatedSummary = async (req, res) => {
  try {
    let fiscalYear;
    // Determine fiscal year
    if (req.query.fiscalYear) {
      fiscalYear = parseInt(req.query.fiscalYear, 10);
    } else {
      const activeSetting = await Settings.findOne({ isActive: true });
      if (!activeSetting)
        return res.status(404).json({ error: "No active settings found." });
      fiscalYear = activeSetting.fiscalYear;
    }

    // Build filter from query params
    const { region, department } = req.query;
    const filter = { fiscalYear };
    if (region && region.trim()) filter.region = region.trim();
    if (department && department.trim()) filter.department = department.trim();

    // Fetch data
    const psEntries = await PersonnelServices.find(filter);
    const mooeEntries = await MOOEProposal.find(filter)
      .populate({ path: "uacsCode", select: "uacsCode lineItem subLineItem accountingTitle" });
    
    // Properly populate the category field
    const coEntries = await CapitalOutlay.find(filter)
      .populate({ 
        path: "category", 
        select: "categoryName _id",
        model: Category
      });

    // Sum Personnel Services fields (excluding annualSalary)
    const psFieldsToSum = [
      "PERA", "RATA", "honoraria", "uniformALLOWANCE",
      "subsistenceAllowance", "subsistenceAllowanceMDS", "subsistenceAllowanceST",
      "productivityIncentive", "medical", "childrenAllowance", "meal",
      "cashGift", "midyearBonus", "yearEndBonus", "retirementBenefits",
      "terminalLeave", "earnedLeaves", "gsisPremium", "pagibigPremium",
      "philhealthPremium", "employeeCompensation", "overtimePay", "hazardPay",
      "courtAppearance", "loyaltyAward"
    ];
    const psSummary = {};
    psFieldsToSum.forEach(field => {
      psSummary[field] = psEntries.reduce(
        (sum, e) => sum + (Number(e[field]) || 0),
        0
      );
    });

    // Compute annualSalary breakdown by appointment type
    const permAnnual = psEntries
      .filter(e => e.statusOfAppointment.toUpperCase() === "PERMANENT")
      .reduce((sum, e) => sum + (Number(e.annualSalary) || 0), 0);
    const casualAnnual = psEntries
      .filter(e => e.statusOfAppointment.toUpperCase() === "CASUAL")
      .reduce((sum, e) => sum + (Number(e.annualSalary) || 0), 0);
    psSummary.annualSalaryPermanent = permAnnual;
    psSummary.annualSalaryCasual    = casualAnnual;

    // Map MOOE entries
    const mooe = mooeEntries.map(entry => ({
      uacsCode: entry.uacsCode?.uacsCode || entry.uacsCode,
      lineItem: entry.uacsCode?.lineItem,
      subLineItem: entry.sublineItem || entry.uacsCode?.subLineItem,
      accountingTitle: entry.accountingTitle || entry.uacsCode?.accountingTitle,
      amount: entry.amount,
      region: entry.region,
      department: entry.department
    }));

    // Map Capital Outlay entries with proper category handling
    const co = await Promise.all(coEntries.map(async (entry) => {
      let categoryName = 'Uncategorized';
      
      // If category is populated, use its categoryName
      if (entry.category && typeof entry.category === 'object' && entry.category.categoryName) {
        categoryName = entry.category.categoryName;
      } 
      // If category is an ObjectId, fetch the category
      else if (entry.category && typeof entry.category !== 'object') {
        try {
          const categoryDoc = await Category.findById(entry.category);
          if (categoryDoc) {
            categoryName = categoryDoc.categoryName;
          }
        } catch (err) {
          console.error(`Error fetching category for ID ${entry.category}:`, err);
        }
      }
      
      return {
        category: categoryName,
        particulars: entry.particulars || '',
        subLineItem: entry.sublineItem || '',
        accountingTitle: entry.accountingTitle || '',
        uacsCode: entry.uacsCode || '',
        cost: entry.cost || 0,
        region: entry.region || '',
        department: entry.department || ''
      };
    }));

    // Totals
    const mooeTotal = mooe.reduce((sum, e) => sum + (Number(e.amount) || 0), 0);
    const coTotal   = co.reduce((sum, e) => sum + (Number(e.cost)   || 0), 0);

    // Return consolidated summary
    res.json({ fiscalYear, psSummary, mooe, mooeTotal, co, coTotal });
  } catch (err) {
    console.error("Error in getConsolidatedSummary:", err);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

module.exports = { getConsolidatedSummary };
