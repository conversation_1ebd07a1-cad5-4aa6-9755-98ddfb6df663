const Retiree = require("../models/Retiree");
const PersonnelServices = require("../models/PersonnelServices");
const Settings = require("../models/Settings");

/**
 * Get all retirees with pagination
 */
exports.getAllRetirees = async (req, res) => {
  try {
    console.log("Fetching all retirees");
    const retirees = await Retiree.find().sort({ createdAt: -1 });
    console.log(`Found ${retirees.length} retiree records`);
    
    // Log a sample record if available
    if (retirees.length > 0) {
      console.log("Sample retiree record:", retirees[0]);
    }
    
    res.status(200).json(retirees);
  } catch (error) {
    console.error("Error fetching retirees:", error);
    res.status(500).json({ message: error.message });
  }
};

/**
 * Get a single retiree by ID
 */
exports.getRetireeById = async (req, res) => {
  try {
    const { id } = req.params;
    const retiree = await Retiree.findById(id);
    
    if (!retiree) {
      return res.status(404).json({ message: "Retiree not found" });
    }
    
    return res.status(200).json(retiree);
  } catch (error) {
    console.error("Error in getRetireeById:", error);
    return res.status(500).json({ message: "Server error", error: error.message });
  }
};

/**
 * Create a new retiree record
 */
exports.createRetiree = async (req, res) => {
  try {
    console.log("Creating retiree with data:", req.body);
    
    // Convert string amounts to numbers
    const formData = {
      ...req.body,
      terminalLeave: parseFloat(req.body.terminalLeave) || 0,
      retirementGratuity: parseFloat(req.body.retirementGratuity) || 0,
      total: parseFloat(req.body.total) || 
             (parseFloat(req.body.terminalLeave) || 0) + 
             (parseFloat(req.body.retirementGratuity) || 0)
    };
    
    const newRetiree = new Retiree(formData);
    await newRetiree.save();
    
    console.log("Retiree created successfully:", newRetiree);
    
    res.status(201).json({ 
      message: "Retiree record created successfully", 
      retiree: newRetiree 
    });
  } catch (error) {
    console.error("Error creating retiree:", error);
    res.status(500).json({ message: error.message });
  }
};

/**
 * Update a retiree record
 */
exports.updateRetiree = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      employeeNumber,
      employeeFullName,
      positionTitle,
      department,
      division,
      region,
      retirementType,
      dateOfRetirement,
      terminalLeave,
      retirementGratuity,
      processBy,
      processDate,
      fiscalYear,
      budgetType,
    } = req.body;
    
    // Get active settings if fiscalYear not provided
    const settings = fiscalYear
      ? await Settings.findOne({ fiscalYear, isActive: true }).lean()
      : await Settings.findOne({ isActive: true }).lean();
    
    if (!settings) {
      return res.status(400).json({ message: "Active settings not found." });
    }
    
    const usedFiscalYear = fiscalYear || settings.fiscalYear;
    const usedBudgetType = budgetType || settings.budgetType;
    
    // Update retiree record
    const updatedRetiree = await Retiree.findByIdAndUpdate(
      id,
      {
        employeeNumber,
        employeeFullName,
        positionTitle,
        department,
        division,
        region,
        retirementType,
        dateOfRetirement,
        terminalLeave: Number(terminalLeave),
        retirementGratuity: Number(retirementGratuity),
        total: Number(terminalLeave) + Number(retirementGratuity),
        processBy,
        processDate,
        fiscalYear: usedFiscalYear,
        budgetType: usedBudgetType,
      },
      { new: true }
    );
    
    if (!updatedRetiree) {
      return res.status(404).json({ message: "Retiree not found" });
    }
    
    // Update PersonnelServices record
    const personnel = await PersonnelServices.findOne({
      employeeNumber,
      fiscalYear: usedFiscalYear,
    });
    
    if (personnel) {
      personnel.terminalLeave = Number(terminalLeave);
      personnel.retirementBenefits = Number(retirementGratuity);
      
      // Recalculate Total
      const numericFields = [
        "annualSalary", "RATA", "PERA", "uniformALLOWANCE", "productivityIncentive",
        "medical", "meal", "cashGift", "midyearBonus", "yearEndBonus",
        "gsisPremium", "philhealthPremium", "pagibigPremium", "employeeCompensation",
        "subsistenceAllowanceMDS", "subsistenceAllowanceST", "overtimePay", "loyaltyAward",
        "earnedLeaves", "retirementBenefits", "terminalLeave", "courtAppearance",
        "hazardPay", "subsistenceAllowance", "honoraria", "childrenAllowance"
      ];
      
      const newTotal = numericFields.reduce(
        (acc, field) => acc + (Number(personnel[field]) || 0),
        0
      );
      
      personnel.Total = newTotal;
      await personnel.save();
    }
    
    return res.status(200).json(updatedRetiree);
  } catch (error) {
    console.error("Error in updateRetiree:", error);
    return res.status(500).json({ message: "Server error", error: error.message });
  }
};

/**
 * Delete a retiree record
 */
exports.deleteRetiree = async (req, res) => {
  try {
    const { id } = req.params;
    const retiree = await Retiree.findById(id);
    
    if (!retiree) {
      return res.status(404).json({ message: "Retiree not found" });
    }
    
    // First, update the PersonnelServices record to remove retirement benefits
    const personnel = await PersonnelServices.findOne({
      employeeNumber: retiree.employeeNumber,
      fiscalYear: retiree.fiscalYear,
    });
    
    if (personnel) {
      personnel.terminalLeave = 0;
      personnel.retirementBenefits = 0;
      
      // Recalculate Total
      const numericFields = [
        "annualSalary", "RATA", "PERA", "uniformALLOWANCE", "productivityIncentive",
        "medical", "meal", "cashGift", "midyearBonus", "yearEndBonus",
        "gsisPremium", "philhealthPremium", "pagibigPremium", "employeeCompensation",
        "subsistenceAllowanceMDS", "subsistenceAllowanceST", "overtimePay", "loyaltyAward",
        "earnedLeaves", "retirementBenefits", "terminalLeave", "courtAppearance",
        "hazardPay", "subsistenceAllowance", "honoraria", "childrenAllowance"
      ];
      
      const newTotal = numericFields.reduce(
        (acc, field) => acc + (Number(personnel[field]) || 0),
        0
      );
      
      personnel.Total = newTotal;
      await personnel.save();
    }
    
    // Then delete the retiree record
    await Retiree.findByIdAndDelete(id);
    
    return res.status(200).json({ message: "Retiree record deleted successfully" });
  } catch (error) {
    console.error("Error in deleteRetiree:", error);
    return res.status(500).json({ message: "Server error", error: error.message });
  }
};

/**
 * Get summary of retirees by type
 */
exports.getRetireesSummary = async (req, res) => {
  try {
    const { fiscalYear } = req.query;
    
    // Get active settings if fiscalYear not provided
    const settings = fiscalYear
      ? await Settings.findOne({ fiscalYear }).lean()
      : await Settings.findOne({ isActive: true }).lean();
    
    if (!settings) {
      return res.status(400).json({ message: "Settings not found." });
    }
    
    const usedFiscalYear = fiscalYear || settings.fiscalYear;
    
    // Get summary for Compulsory retirements
    const compulsorySummary = await Retiree.aggregate([
      { 
        $match: { 
          fiscalYear: usedFiscalYear,
          retirementType: "Compulsory"
        } 
      },
      { 
        $group: { 
          _id: null, 
          terminalLeaveTotal: { $sum: "$terminalLeave" },
          retirementGratuityTotal: { $sum: "$retirementGratuity" },
          total: { $sum: "$total" },
          count: { $sum: 1 }
        } 
      }
    ]);
    
    // Get summary for Optional retirements
    const optionalSummary = await Retiree.aggregate([
      { 
        $match: { 
          fiscalYear: usedFiscalYear,
          retirementType: "Optional"
        } 
      },
      { 
        $group: { 
          _id: null, 
          terminalLeaveTotal: { $sum: "$terminalLeave" },
          retirementGratuityTotal: { $sum: "$retirementGratuity" },
          total: { $sum: "$total" },
          count: { $sum: 1 }
        } 
      }
    ]);
    
    return res.status(200).json({
      fiscalYear: usedFiscalYear,
      compulsory: compulsorySummary[0] || {
        terminalLeaveTotal: 0,
        retirementGratuityTotal: 0,
        total: 0,
        count: 0
      },
      optional: optionalSummary[0] || {
        terminalLeaveTotal: 0,
        retirementGratuityTotal: 0,
        total: 0,
        count: 0
      }
    });
  } catch (error) {
    console.error("Error in getRetireesSummary:", error);
    return res.status(500).json({ message: "Server error", error: error.message });
  }
};


