const Proposal = require("../models/Proposal");
const PersonnelServices = require("../models/personnelServices");
const MOOEProposal = require("../models/mooeProposals");
const CapitalOutlay = require("../models/CapitalOutlay");
const BudgetarySupport = require("../models/BudgetarySupport");
const Settings = require("../models/Settings");

// Get overview statistics for the dashboard
exports.getOverviewStats = async (req, res) => {
  try {
    // Get active fiscal year
    const activeSettings = await Settings.findOne({ isActive: true });
    const fiscalYear = activeSettings?.fiscalYear || new Date().getFullYear().toString();
    
    console.log("Using fiscal year:", fiscalYear);

    // Count proposals by status - include Budgetary Support
    const [personnelCount, mooeCount, capitalCount, budgetarySupportCount] = await Promise.all([
      PersonnelServices.countDocuments({ fiscalYear }),
      MOOEProposal.countDocuments({ fiscalYear }),
      CapitalOutlay.countDocuments({ fiscalYear }),
      BudgetarySupport.countDocuments({ fiscalYear })
    ]);
    
    console.log("Counts by type:", { personnelCount, mooeCount, capitalCount, budgetarySupportCount });

    // Count proposals by status
    const pendingPersonnel = await PersonnelServices.countDocuments({ 
      fiscalYear, 
      status: { $nin: ["Approved", "Rejected"] } 
    });
    const pendingMOOE = await MOOEProposal.countDocuments({ 
      fiscalYear, 
      status: { $nin: ["Approved", "Rejected"] } 
    });
    const pendingCapital = await CapitalOutlay.countDocuments({
      fiscalYear,
      status: { $nin: ["Approved", "Rejected"] }
    });
    const pendingBudgetarySupport = await BudgetarySupport.countDocuments({
      fiscalYear,
      status: { $nin: ["Approved", "Rejected"] }
    });

    console.log("Pending counts:", { pendingPersonnel, pendingMOOE, pendingCapital, pendingBudgetarySupport });

    const approvedPersonnel = await PersonnelServices.countDocuments({ 
      fiscalYear, 
      status: "Approved" 
    });
    const approvedMOOE = await MOOEProposal.countDocuments({ 
      fiscalYear, 
      status: "Approved" 
    });
    const approvedCapital = await CapitalOutlay.countDocuments({
      fiscalYear,
      status: "Approved"
    });
    const approvedBudgetarySupport = await BudgetarySupport.countDocuments({
      fiscalYear,
      status: "Approved"
    });

    console.log("Approved counts:", { approvedPersonnel, approvedMOOE, approvedCapital, approvedBudgetarySupport });

    const rejectedPersonnel = await PersonnelServices.countDocuments({ 
      fiscalYear, 
      status: "Rejected" 
    });
    const rejectedMOOE = await MOOEProposal.countDocuments({ 
      fiscalYear, 
      status: "Rejected" 
    });
    const rejectedCapital = await CapitalOutlay.countDocuments({
      fiscalYear,
      status: "Rejected"
    });
    const rejectedBudgetarySupport = await BudgetarySupport.countDocuments({
      fiscalYear,
      status: "Rejected"
    });

    console.log("Rejected counts:", { rejectedPersonnel, rejectedMOOE, rejectedCapital, rejectedBudgetarySupport });

    // Calculate totals - include Budgetary Support
    const totalProposals = personnelCount + mooeCount + capitalCount + budgetarySupportCount;
    const pendingProposals = pendingPersonnel + pendingMOOE + pendingCapital + pendingBudgetarySupport;
    const approvedProposals = approvedPersonnel + approvedMOOE + approvedCapital + approvedBudgetarySupport;
    const rejectedProposals = rejectedPersonnel + rejectedMOOE + rejectedCapital + rejectedBudgetarySupport;

    console.log("Final totals:", { totalProposals, pendingProposals, approvedProposals, rejectedProposals });

    res.status(200).json({
      totalProposals,
      pendingProposals,
      approvedProposals,
      rejectedProposals,
      fiscalYear
    });
  } catch (error) {
    console.error("Error fetching overview stats:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

