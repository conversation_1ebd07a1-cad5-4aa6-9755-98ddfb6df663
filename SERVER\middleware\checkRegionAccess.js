const { checkUserRegionAccess } = require('../controllers/UserRegionAssignmentController');

module.exports = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { region } = req.body;
    
    // If no region in request, allow access
    if (!region) {
      return next();
    }
    
    // Check if user has access to this region
    const hasAccess = await checkUserRegionAccess(userId, region);
    
    if (!hasAccess) {
      return res.status(403).json({ 
        message: "You don't have permission to process proposals for this region" 
      });
    }
    
    next();
  } catch (error) {
    console.error('Error in checkRegionAccess middleware:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

