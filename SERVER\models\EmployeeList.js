const mongoose = require("mongoose");

const EmployeeSchema = mongoose.Schema(
  {
    Department: {
      type: String,
      required: true,
    },
    PositionTitle: {
      type: String,
      required: true,
    },
    Division: {
      type: String,
    },
    Section: {
      type: String,
    },
    Region: {
      type: String,
      required: true,
    },
    StatusOfAppointment: {
      type: String,
      required: true,
    },
    SG: {
      type: Number,
      required: true,
    },
    Step: {
      type: Number,
      required: true,
    },
    JG: {
      type: String,
      required: true,
    },
    Rate: {
      type: Number,
      required: true,
    },
    EmployeeID: {
      type: String,
    },
    EmployeeFullName: {
      type: String,
      required: true,
    },
    DateOfAppointment: {
      type: Date,
      required: true,
    },
    DateOfBirth: {
      type: Date,
    },
    employeeStatus: {
      type: String,
      enum: ["Active", "Inactive"],
      default: "Active",
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

const Employee = mongoose.model("employee", EmployeeSchema);

module.exports = Employee;
