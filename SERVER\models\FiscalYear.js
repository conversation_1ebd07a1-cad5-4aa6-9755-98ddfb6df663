const mongoose = require('mongoose');

const fiscalYearSchema = new mongoose.Schema({
  year: {
    type: String,
    required: true,
    unique: true,
    validate: {
      validator: function (value) {
        // Validate format "FY-YYYY"
        return /^FY-\d{4}$/.test(value);
      },
      message: (props) => `${props.value} is not a valid fiscal year format!`,
    },
  },
  startDate: {
    type: Date,
    required: true,
  },
  endDate: {
    type: Date,
    required: true,
  },
});

const FiscalYear = mongoose.model('FiscalYear', fiscalYearSchema);
module.exports = FiscalYear;
