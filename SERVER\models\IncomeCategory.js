const mongoose = require("mongoose");

const incomeCategorySchema = new mongoose.Schema({
  incomeCategoryName: { type: String, required: true, unique: true },
  name: { type: String, unique: true }, // Add explicit name field
  description: { type: String, default: "" },
  incomeSubcategoryName: [{ type: String }],
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Pre-save middleware to ensure name field is set
incomeCategorySchema.pre('save', function(next) {
  // Set the name field to match incomeCategoryName if not explicitly set
  if (this.incomeCategoryName && !this.name) {
    this.name = this.incomeCategoryName;
  }
  next();
});

const IncomeCat = mongoose.model("IncomeCategory", incomeCategorySchema);

module.exports = IncomeCat;
