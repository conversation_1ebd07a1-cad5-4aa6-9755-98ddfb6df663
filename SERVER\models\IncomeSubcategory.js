const mongoose = require("mongoose");

const incomeSubcategorySchema = new mongoose.Schema(
  {
    incomeSubcategoryName: { 
      type: String, 
      required: true
    },
    name: {
      type: String,
      unique: true
    }
  },
  { timestamps: true }
);

// Pre-save middleware to ensure name field is set
incomeSubcategorySchema.pre('save', function(next) {
  // Set the name field to match incomeSubcategoryName
  if (this.incomeSubcategoryName) {
    this.name = this.incomeSubcategoryName;
  }
  next();
});

module.exports = mongoose.models.IncomeSubcategory || 
  mongoose.model("IncomeSubcategory", incomeSubcategorySchema);
