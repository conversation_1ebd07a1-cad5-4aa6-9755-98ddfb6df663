const mongoose = require("mongoose");

const EmployeeMasterSchema = mongoose.Schema({
Department: {
    type: String,
    required: true
},
Division
: {
    type: String,

},
Section
: {
    type: String,

},
Unit
: {
    type: String,
    
},
ItemNumber
: {
    type: String,
    
},
PositionTitle
: {
    type: String,
    required: true
},
Status
: {
    type: String,
    enum: ["PERMANENT", "CASUAL", "COS"],
    required: true
},
SG
: {
    type: Number,
    required: true
},
Step
: {
    type: Number,
    required: true
}, 
    JG : {
        type: Number,
        required: true
    },
 
Rate: {
    type: Number,
    required: true
},
Charging : {
    type: String,
    required: true
},
EmployeeID : {  
    type: String,

},

EmployeeFullName : {
    type: String,
    required: true
},
});

const EmployeeMaster = mongoose.model("employee_master", EmployeeMasterSchema);
module.exports = EmployeeMaster;