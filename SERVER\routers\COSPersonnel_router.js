const {
  bulkAddCOSPersonnel,
  getAllCOSPersonnel,
  updateCOSPersonnel,
  deleteCOSPersonnel,
  getGrandTotalCOS
} = require("../controllers/COSPersonnelController");

const Router = require("express").Router;
const checkDueDate = require("../middleware/checkDueDate");

const cosPersonnelRouter = Router();

cosPersonnelRouter.get("/cosPersonnels", getAllCOSPersonnel);
cosPersonnelRouter.post("/cos-personnel/bulk-add", checkDueDate, bulkAddCOSPersonnel);
cosPersonnelRouter.put("/cos-personnel/:id", checkDueDate, updateCOSPersonnel);
cosPersonnelRouter.delete("/cos-personnel/:id", checkDueDate, deleteCOSPersonnel);
cosPersonnelRouter.get("/grandtotalCOS", getGrandTotalCOS);

module.exports = cosPersonnelRouter;
