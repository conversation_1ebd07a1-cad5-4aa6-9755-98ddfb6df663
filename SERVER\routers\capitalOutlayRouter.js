const {
  getAllCapitalOutlays,
  addCapitalOutlay,
  editCapitalOutlay,
  deleteCapitalOutlay,
  getCapitalOutlayList,
  getSublineItems,
  getAccountingTitles,
  deleteAllCapitalOutlays,
} = require("../controllers/capitalOutlayController");

const Router = require("express").Router;
const checkDueDate = require("../middleware/checkDueDate");
const CapitalOutlay = require("../models/CapitalOutlay"); // New model import

const capitalOutlayRouter = Router();

capitalOutlayRouter.get("/capital-outlays", getAllCapitalOutlays);
capitalOutlayRouter.post("/capital-outlays", checkDueDate, addCapitalOutlay);
capitalOutlayRouter.put("/capital-outlays/:id", checkDueDate, editCapitalOutlay);
capitalOutlayRouter.delete("/capital-outlays/:id", checkDueDate, deleteCapitalOutlay);

capitalOutlayRouter.get("/capital-outlay-list", getCapitalOutlayList);
capitalOutlayRouter.get("/subline-items", getSublineItems);
capitalOutlayRouter.get("/accounting-titles", getAccountingTitles);
capitalOutlayRouter.delete("/deleteAllCapitalOutlays", deleteAllCapitalOutlays);
// Get Capital Outlay by parameters
capitalOutlayRouter.get("/capital-outlay/getByParams", async (req, res) => {
  try {
    const { fiscalYear, budgetType, processBy, region, status } = req.query;
    
    console.log("Fetching Capital Outlay with params:", { fiscalYear, budgetType, processBy, region, status });
    
    const query = {};
    if (fiscalYear) query.fiscalYear = fiscalYear;
    if (budgetType) query.budgetType = budgetType;
    if (processBy) query.processBy = processBy;
    if (region) query.region = region;
    if (status) query.status = status;
    
    const capitalOutlay = await CapitalOutlay.find(query).lean();
    
    console.log(`Found ${capitalOutlay.length} Capital Outlay records`);
    res.status(200).json(capitalOutlay);
  } catch (error) {
    console.error("Error fetching Capital Outlay by params:", error);
    res.status(500).json({ message: "Failed to fetch Capital Outlay data" });
  }
});
module.exports = capitalOutlayRouter;
