const express = require("express");
const {
  createEmployee,
  getAllEmployees,
  getEmployeeById,
  updateEmployee,
  deleteEmployee,
} = require("../controllers/employee_master_controller");

const employeeMasterRouter = express.Router();

employeeMasterRouter.get("/employees", getAllEmployees);
employeeMasterRouter.post("/employees", createEmployee);
employeeMasterRouter.get("/employees/:id", getEmployeeById);
employeeMasterRouter.put("/employees/:id", updateEmployee);
employeeMasterRouter.delete("/employees/:id", deleteEmployee);

module.exports = employeeMasterRouter;