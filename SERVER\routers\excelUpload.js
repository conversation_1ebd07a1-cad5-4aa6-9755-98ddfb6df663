const express = require("express");
const multer = require("multer");
const xlsx = require("xlsx");
const Employee = require("../models/EmployeeList"); // Make sure your Employee model exists

const router = express.Router();

// Configure Multer (Store file in memory)
const storage = multer.memoryStorage();
const upload = multer({ storage });

// 📌 Upload and Process Excel File
router.post("/upload", upload.single("file"), async (req, res) => {
  try {
    const workbook = xlsx.read(req.file.buffer, { type: "buffer" });
    const sheetName = workbook.SheetNames[0];
    const sheetData = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    for (let record of sheetData) {
      // Convert Excel date format to JavaScript Date if present
      if (record.DateOfAppointment) {
        // Handle different date formats - if it's an Excel serial number
        if (typeof record.DateOfAppointment === 'number') {
          // Convert Excel date serial number to JavaScript Date
          record.DateOfAppointment = new Date(Math.round((record.DateOfAppointment - 25569) * 86400 * 1000));
        } else if (typeof record.DateOfAppointment === 'string') {
          // Try to parse the date string
          record.DateOfAppointment = new Date(record.DateOfAppointment);
        }
      } else {
        // If DateOfAppointment is not provided, set a default value
        record.DateOfAppointment = new Date();
      }

      const existingEmployee = await Employee.findOne({ EmployeeID: record.EmployeeID });

      if (existingEmployee) {
        await Employee.updateOne({ EmployeeID: record.EmployeeID }, { $set: record });
      } else {
        await Employee.create(record);
      }
    }

    res.json({ message: "Excel file processed successfully!" });
  } catch (error) {
    console.error("Error processing file:", error);
    res.status(500).json({ error: "Error uploading file" });
  }
});

module.exports = router;
