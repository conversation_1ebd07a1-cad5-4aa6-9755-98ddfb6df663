const {
  getAllIncomeSubcategories,
  addIncomeSubcategory,
  editIncomeSubcategory,
  deleteIncomeSubcategory,
} = require("../controllers/IncomeSubcategoryController");

const Router = require("express").Router;

const incomeSubcategoryRouter = Router();

// Get all income subcategories
incomeSubcategoryRouter.get("/income-subcategories", getAllIncomeSubcategories);

// Add a new income subcategory
incomeSubcategoryRouter.post("/income-subcategories", addIncomeSubcategory);

// Edit an existing income subcategory
incomeSubcategoryRouter.put("/income-subcategories/:id", editIncomeSubcategory);

// Delete an income subcategory
incomeSubcategoryRouter.delete("/income-subcategories/:id", deleteIncomeSubcategory);

module.exports = incomeSubcategoryRouter;