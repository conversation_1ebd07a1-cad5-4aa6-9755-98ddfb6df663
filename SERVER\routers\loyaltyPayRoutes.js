const {
  createLoyaltyPay,
  getAllLoyaltyPays,
  updateLoyaltyPay,
  deleteLoyaltyPay,
} = require("../controllers/loyaltyPayController");

const Router = require("express").Router;
const checkDueDate = require("../middleware/checkDueDate");

const loyaltyPayRouter = Router();

loyaltyPayRouter.get("/loyalty-pay", getAllLoyaltyPays);
loyaltyPayRouter.post("/loyalty-pay", checkDueDate, createLoyaltyPay);
loyaltyPayRouter.put("/loyalty-pay/:id", checkDueDate, updateLoyaltyPay);
loyaltyPayRouter.delete("/loyalty-pay/:id", checkDueDate, deleteLoyaltyPay);

module.exports = loyaltyPayRouter;
