const {
  createOvertimePay,
  getAllOvertimePays,
  updateOvertimePay,
  deleteOvertimePay,
  getSumOfOvertimeAmount,
} = require("../controllers/overtimePayController");

const Router = require("express").Router;
const checkDueDate = require("../middleware/checkDueDate");

const overtimeRouter = Router();

overtimeRouter.get("/overtime-pay", getAllOvertimePays);
overtimeRouter.post("/overtime-pay", checkDueDate, createOvertimePay);
overtimeRouter.put("/overtime-pay/:id", checkDueDate, updateOvertimePay);
overtimeRouter.delete("/overtime-pay/:id", checkDueDate, deleteOvertimePay);
overtimeRouter.get("/overtime-pay/sum", getSumOfOvertimeAmount);

module.exports = overtimeRouter;
