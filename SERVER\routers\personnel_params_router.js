const { getPersonnelByParams } = require("../controllers/personnel_params_controller");
const Router = require("express").Router;

const personnelParamsRouter = Router();

// Get personnel by parameters
personnelParamsRouter.get("/getpersonnels/byParams", getPersonnelByParams);

// Legacy route for backward compatibility
personnelParamsRouter.get("/api/personnel/getByParams", getPersonnelByParams);

module.exports = personnelParamsRouter;