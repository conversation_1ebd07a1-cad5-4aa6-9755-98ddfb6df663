
const {
  getProposals,
  createProposal,
  getProposalById,
  updateProposal,
  deleteProposal,
  updateProposalStatus,
  getAllProposals,
  approveProposal,    
  rejectAllProposals,      
  getMyProposals,
  deleteAllProposals,
  saveAsDraft,
  getAllUserProposals,
  updateMissingRegions,
  returnProposal,
  getProposalDetails,
  saveDraftProposal,
  getDraftProposal
} = require("../controllers/proposalController");

const Router = require("express").Router;
const Proposal = require("../models/Proposal");
const DraftProposal = require("../models/DraftProposal");

const proposalRouter = Router();

// Get all proposals summary
proposalRouter.get("/summary", getAllProposals);

// Get all proposals
proposalRouter.get("/proposals", getProposals);

//Get all my proposals
proposalRouter.get("/myproposals", getMyProposals);

// Get all proposals for the current user including drafts
proposalRouter.get("/myproposals/all", getAllUserProposals);

// Get proposal details - IMPORTANT: Place this BEFORE the /:id route
if (typeof getProposalDetails === 'function') {
  proposalRouter.get('/proposals/details', getProposalDetails);
}

// Get a single proposal by ID - This should come AFTER the /details route
proposalRouter.get("/proposals/:id", getProposalById);

// Add a new proposal
proposalRouter.post("/proposals", createProposal);

// Edit an existing proposal
proposalRouter.put("/proposals/:id", updateProposal);

// Delete a proposal
proposalRouter.delete("/proposals/:id", deleteProposal);

// Update proposal status
proposalRouter.post("/updateProposalStatus", updateProposalStatus);

// Approve a proposal
proposalRouter.put("/approveProposal", approveProposal);

// Reject a proposal
proposalRouter.put("/rejectAllProposals", rejectAllProposals);

// Delete all proposals
proposalRouter.delete("/deleteAllProposals", deleteAllProposals);

// Save as Draft
proposalRouter.post("/saveAsDraft", saveAsDraft);

// Add this new route
proposalRouter.post("/update-missing-regions", updateMissingRegions);

// Return a proposal
proposalRouter.post("/returnProposal", returnProposal);

// Add a simplified fallback endpoint
proposalRouter.get('/proposal-simple', async (req, res) => {
  try {
    const { fiscalYear, budgetType, processBy, region } = req.query;
    
    if (!fiscalYear || !budgetType || !processBy || !region) {
      return res.status(400).json({ message: "Missing required parameters" });
    }
    
    // Return basic information without database query
    const basicProposal = {
      fiscalYear,
      budgetType,
      processBy,
      region,
      cobExpenditures: "Unknown",
      status: "Unknown",
      submittedDate: null
    };
    
    res.status(200).json(basicProposal);
  } catch (error) {
    console.error("Error in simplified endpoint:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Get draft proposal by ID
if (typeof getDraftProposal === 'function') {
  proposalRouter.get('/proposals/draft/:id', getDraftProposal);
}

// Add route to save draft proposals
if (typeof saveDraftProposal === 'function') {
  proposalRouter.post('/proposals/draft', saveDraftProposal);
}

module.exports = proposalRouter;
