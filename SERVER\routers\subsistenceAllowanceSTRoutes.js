const {
  createSubsistenceST,
  getAllSubsistenceST,
  updateSubsistenceST,
  deleteSubsistenceST,
} = require("../controllers/subsistenceAllowanceSTController");

const Router = require("express").Router;

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  dueDateProtectedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

const subsistenceSTRouter = Router();

// 🔒 SECURED ROUTES

subsistenceSTRouter.get("/subsistence-allowance-st", ...authenticatedRoute(), getAllSubsistenceST);
subsistenceSTRouter.post("/subsistence-allowance-st", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), createSubsistenceST);
subsistenceSTRouter.put("/subsistence-allowance-st/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), updateSubsistenceST);
subsistenceSTRouter.delete("/subsistence-allowance-st/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), deleteSubsistenceST);

module.exports = subsistenceSTRouter;
