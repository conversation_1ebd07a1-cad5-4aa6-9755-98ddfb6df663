// Add missing Chart of Accounts data for MOOE
const mongoose = require('mongoose');
require('dotenv').config();

const ChartOfAccounts = require('../models/chartOfAccounts');

async function addMOOEChartOfAccounts() {
  try {
    console.log('🔧 Adding MOOE Chart of Accounts...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB\n');
    
    // Check existing data first
    const totalRecords = await ChartOfAccounts.countDocuments();
    console.log('📊 Current Chart of Accounts records:', totalRecords);
    
    // Check if MOOE records already exist
    const existingMOOE = await ChartOfAccounts.find({
      accountClass: "Expense",
      lineItem: "Maintenance and Other Operating Expenses"
    }).lean();
    
    console.log('📋 Existing MOOE records:', existingMOOE.length);
    
    if (existingMOOE.length > 0) {
      console.log('✅ MOOE Chart of Accounts already exist!');
      existingMOOE.forEach((record, index) => {
        console.log(`${index + 1}.`, {
          sublineItem: record.sublineItem,
          accountingTitle: record.accountingTitle,
          uacsCode: record.uacsCode
        });
      });
      return;
    }
    
    // Sample MOOE Chart of Accounts data
    const mooeChartOfAccounts = [
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Traveling Expenses",
        accountingTitle: "Traveling Expenses - Local",
        uacsCode: "5-02-01-010",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Traveling Expenses",
        accountingTitle: "Traveling Expenses - Foreign",
        uacsCode: "5-02-01-020",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Training and Scholarship Expenses",
        accountingTitle: "Training Expenses",
        uacsCode: "5-02-02-010",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Training and Scholarship Expenses",
        accountingTitle: "Scholarship Grants/Expenses",
        uacsCode: "5-02-02-020",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Supplies and Materials Expenses",
        accountingTitle: "Office Supplies Expenses",
        uacsCode: "5-02-03-010",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Supplies and Materials Expenses",
        accountingTitle: "Accountable Forms Expenses",
        uacsCode: "5-02-03-020",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Supplies and Materials Expenses",
        accountingTitle: "Non-Accountable Forms Expenses",
        uacsCode: "5-02-03-030",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Supplies and Materials Expenses",
        accountingTitle: "Fuel, Oil and Lubricants Expenses",
        uacsCode: "5-02-03-050",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Supplies and Materials Expenses",
        accountingTitle: "Textbooks and Instructional Materials Expenses",
        uacsCode: "5-02-03-070",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Utilities Expenses",
        accountingTitle: "Water Expenses",
        uacsCode: "5-02-04-010",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Utilities Expenses",
        accountingTitle: "Electricity Expenses",
        uacsCode: "5-02-04-020",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Communication Expenses",
        accountingTitle: "Postage and Courier Services",
        uacsCode: "5-02-05-010",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Communication Expenses",
        accountingTitle: "Telephone Expenses",
        uacsCode: "5-02-05-020",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Communication Expenses",
        accountingTitle: "Internet Subscription Expenses",
        uacsCode: "5-02-05-030",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Repairs and Maintenance",
        accountingTitle: "Repairs and Maintenance - Motor Vehicles",
        uacsCode: "5-02-13-010",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Repairs and Maintenance",
        accountingTitle: "Repairs and Maintenance - Office Equipment",
        uacsCode: "5-02-13-020",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Repairs and Maintenance",
        accountingTitle: "Repairs and Maintenance - ICT Equipment",
        uacsCode: "5-02-13-030",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Other Maintenance and Operating Expenses",
        accountingTitle: "Advertising Expenses",
        uacsCode: "5-02-99-010",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Other Maintenance and Operating Expenses",
        accountingTitle: "Printing and Publication Expenses",
        uacsCode: "5-02-99-020",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Other Maintenance and Operating Expenses",
        accountingTitle: "Representation Expenses",
        uacsCode: "5-02-99-030",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Other Maintenance and Operating Expenses",
        accountingTitle: "Transportation and Delivery Expenses",
        uacsCode: "5-02-99-040",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Other Maintenance and Operating Expenses",
        accountingTitle: "Rent/Lease Expenses",
        uacsCode: "5-02-99-050",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Other Maintenance and Operating Expenses",
        accountingTitle: "Membership Dues and Contributions to Organizations",
        uacsCode: "5-02-99-060",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Other Maintenance and Operating Expenses",
        accountingTitle: "Subscription Expenses",
        uacsCode: "5-02-99-070",
        normalBalance: "Debit"
      },
      {
        accountClass: "Expense",
        lineItem: "Maintenance and Other Operating Expenses",
        sublineItem: "Other Maintenance and Operating Expenses",
        accountingTitle: "Other MOOE",
        uacsCode: "5-02-99-990",
        normalBalance: "Debit"
      }
    ];
    
    console.log('📝 Adding', mooeChartOfAccounts.length, 'MOOE Chart of Accounts records...');
    
    // Insert the records
    const result = await ChartOfAccounts.insertMany(mooeChartOfAccounts);
    
    console.log('✅ Successfully added', result.length, 'MOOE Chart of Accounts records!');
    
    // Verify the insertion
    const newMOOECount = await ChartOfAccounts.countDocuments({
      accountClass: "Expense",
      lineItem: "Maintenance and Other Operating Expenses"
    });
    
    console.log('📊 Total MOOE records now:', newMOOECount);
    
    console.log('\n🎉 MOOE Chart of Accounts setup complete!');
    console.log('The MOOE table should now display data properly.');
    
  } catch (error) {
    console.error('❌ Error adding MOOE Chart of Accounts:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

// Run the script
addMOOEChartOfAccounts();
