// Check Chart of Accounts data
const mongoose = require('mongoose');
require('dotenv').config();

const ChartOfAccounts = require('../models/chartOfAccounts');

async function checkChartOfAccounts() {
  try {
    console.log('🔧 Checking Chart of Accounts...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB\n');
    
    // 1. Check total records
    const totalRecords = await ChartOfAccounts.countDocuments();
    console.log('📊 Total Chart of Accounts records:', totalRecords);
    
    if (totalRecords === 0) {
      console.log('❌ No Chart of Accounts records found!');
      return;
    }
    
    // 2. Check unique accountClass values
    const accountClasses = await ChartOfAccounts.distinct('accountClass');
    console.log('📋 Account Classes:', accountClasses);
    
    // 3. Check unique lineItem values
    const lineItems = await ChartOfAccounts.distinct('lineItem');
    console.log('📋 Line Items:', lineItems);
    
    // 4. Check records with accountClass = "Expense"
    const expenseRecords = await ChartOfAccounts.find({ accountClass: "Expense" }).lean();
    console.log('\n💰 Expense Records:', expenseRecords.length);
    
    if (expenseRecords.length > 0) {
      console.log('Sample expense record:', expenseRecords[0]);
      
      // Check unique lineItems for Expense records
      const expenseLineItems = await ChartOfAccounts.distinct('lineItem', { accountClass: "Expense" });
      console.log('📋 Expense Line Items:', expenseLineItems);
    }
    
    // 5. Check records with lineItem containing "Maintenance"
    const maintenanceRecords = await ChartOfAccounts.find({ 
      lineItem: { $regex: /maintenance/i } 
    }).lean();
    console.log('\n🔧 Records with "Maintenance" in lineItem:', maintenanceRecords.length);
    
    if (maintenanceRecords.length > 0) {
      console.log('Sample maintenance record:', maintenanceRecords[0]);
    }
    
    // 6. Check the exact query used in the controller
    const mooeRecords = await ChartOfAccounts.find({
      accountClass: "Expense",
      lineItem: "Maintenance and Other Operating Expenses"
    }).lean();
    console.log('\n🎯 MOOE Query Results:', mooeRecords.length);
    
    if (mooeRecords.length > 0) {
      console.log('MOOE records found:', mooeRecords);
    } else {
      console.log('❌ No records match the exact MOOE criteria');
      
      // Try variations
      const variations = [
        "Maintenance and Other Operating Expenses",
        "MAINTENANCE AND OTHER OPERATING EXPENSES",
        "maintenance and other operating expenses",
        "Maintenance and Other Operating Expense",
        "MOOE"
      ];
      
      for (const variation of variations) {
        const count = await ChartOfAccounts.countDocuments({
          accountClass: "Expense",
          lineItem: variation
        });
        console.log(`- "${variation}": ${count} records`);
      }
      
      // Check for partial matches
      const partialMatches = await ChartOfAccounts.find({
        accountClass: "Expense",
        lineItem: { $regex: /maintenance.*operating/i }
      }).lean();
      console.log(`- Partial matches (regex): ${partialMatches.length} records`);
      
      if (partialMatches.length > 0) {
        console.log('Partial match examples:');
        partialMatches.forEach((record, index) => {
          console.log(`  ${index + 1}. "${record.lineItem}"`);
        });
      }
    }
    
    // 7. Show sample records for debugging
    console.log('\n📋 Sample Chart of Accounts records:');
    const sampleRecords = await ChartOfAccounts.find().limit(5).lean();
    sampleRecords.forEach((record, index) => {
      console.log(`${index + 1}.`, {
        accountClass: record.accountClass,
        lineItem: record.lineItem,
        sublineItem: record.sublineItem,
        uacsCode: record.uacsCode
      });
    });
    
  } catch (error) {
    console.error('❌ Error checking Chart of Accounts:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

// Run the check
checkChartOfAccounts();
