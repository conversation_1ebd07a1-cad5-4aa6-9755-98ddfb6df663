// Complete the naming convention standardization
const fs = require('fs');
const path = require('path');

console.log('🚀 Completing Naming Convention Standardization\n');

// Define file renames needed
const fileRenames = [
  // Controllers
  {
    old: 'controllers/BudgetManagerOrgController.js',
    new: 'controllers/budgetManagerOrgController.js',
    type: 'controller'
  },
  {
    old: 'controllers/RATAController.js', 
    new: 'controllers/rataController.js',
    type: 'controller'
  },
  {
    old: 'controllers/COSPersonnelController.js',
    new: 'controllers/cosPersonnelController.js', 
    type: 'controller'
  },
  {
    old: 'controllers/ps_annexes_controller.js',
    new: 'controllers/personnelServicesAnnexesController.js',
    type: 'controller'
  },
  
  // Routes
  {
    old: 'routers/BudgetManagerOrgRoutes.js',
    new: 'routers/budgetManagerOrgRoutes.js',
    type: 'router'
  },
  {
    old: 'routers/RATARoutes.js',
    new: 'routers/rataRoutes.js', 
    type: 'router'
  },
  {
    old: 'routers/COSPersonnel_router.js',
    new: 'routers/cosPersonnelRoutes.js',
    type: 'router'
  },
  {
    old: 'routers/ps_annexes.js',
    new: 'routers/personnelServicesAnnexesRoutes.js',
    type: 'router'
  },
  
  // Middleware
  {
    old: 'middleware/check_token.js',
    new: 'middleware/checkToken.js',
    type: 'middleware'
  },
  
  // Models that need renaming
  {
    old: 'models/PS_Annexes.js',
    new: 'models/PersonnelServicesAnnexes.js',
    type: 'model'
  }
];

// Files that need import updates after renaming
const importUpdates = [
  {
    file: 'index.js',
    updates: [
      {
        from: 'require("./routers/BudgetManagerOrgRoutes")',
        to: 'require("./routers/budgetManagerOrgRoutes")'
      },
      {
        from: 'require("./routers/RATARoutes")',
        to: 'require("./routers/rataRoutes")'
      },
      {
        from: 'require("./routers/COSPersonnel_router")',
        to: 'require("./routers/cosPersonnelRoutes")'
      },
      {
        from: 'require("./routers/ps_annexes")',
        to: 'require("./routers/personnelServicesAnnexesRoutes")'
      },
      {
        from: 'require("./middleware/check_token")',
        to: 'require("./middleware/checkToken")'
      }
    ]
  },
  {
    file: 'controllers/personnelServicesAnnexesController.js',
    updates: [
      {
        from: 'require("../models/PS_Annexes")',
        to: 'require("../models/PersonnelServicesAnnexes")'
      }
    ]
  },
  {
    file: 'routers/personnelServicesAnnexesRoutes.js',
    updates: [
      {
        from: 'require("../controllers/ps_annexes_controller")',
        to: 'require("../controllers/personnelServicesAnnexesController")'
      }
    ]
  }
];

// Function to check if file exists
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

// Function to read file
function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.log(`❌ Could not read ${filePath}: ${error.message}`);
    return null;
  }
}

// Function to write file
function writeFile(filePath, content) {
  try {
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  } catch (error) {
    console.log(`❌ Could not write ${filePath}: ${error.message}`);
    return false;
  }
}

// Function to copy file (simulate rename)
function copyFile(oldPath, newPath) {
  try {
    const content = fs.readFileSync(oldPath, 'utf8');
    fs.writeFileSync(newPath, content, 'utf8');
    return true;
  } catch (error) {
    console.log(`❌ Could not copy ${oldPath} to ${newPath}: ${error.message}`);
    return false;
  }
}

// Show what files will be renamed
console.log('📋 Files to be Renamed:\n');
fileRenames.forEach((rename, index) => {
  const exists = fileExists(rename.old);
  const status = exists ? '✅' : '❌';
  console.log(`${index + 1}. ${status} ${rename.old} → ${rename.new}`);
});

console.log('\n📝 Import Updates Needed:\n');
importUpdates.forEach(update => {
  const exists = fileExists(update.file);
  const status = exists ? '✅' : '❌';
  console.log(`${status} ${update.file} (${update.updates.length} updates)`);
});

// Function to apply import updates
function applyImportUpdates() {
  console.log('\n🔄 Applying Import Updates:\n');
  
  importUpdates.forEach(update => {
    console.log(`📝 Updating ${update.file}...`);
    
    const content = readFile(update.file);
    if (!content) return;
    
    let updatedContent = content;
    let changesMade = false;
    
    update.updates.forEach(replacement => {
      if (updatedContent.includes(replacement.from)) {
        updatedContent = updatedContent.replace(replacement.from, replacement.to);
        changesMade = true;
        console.log(`   ✅ Updated import: ${replacement.from.substring(0, 30)}...`);
      }
    });
    
    if (changesMade) {
      if (writeFile(update.file, updatedContent)) {
        console.log(`   💾 ${update.file} updated successfully`);
      }
    } else {
      console.log(`   ℹ️  No changes needed in ${update.file}`);
    }
    console.log('');
  });
}

// Function to show manual steps
function showManualSteps() {
  console.log('\n📋 Manual Steps Required:\n');
  
  const steps = [
    '1. Rename files using your IDE or file manager:',
    ...fileRenames.map(r => `   ${r.old} → ${r.new}`),
    '',
    '2. Update any remaining import statements',
    '3. Test functionality after changes',
    '4. Update client-side references if needed',
    '5. Commit changes incrementally'
  ];
  
  steps.forEach(step => console.log(step));
}

// Execute the completion
console.log('\n🎯 Completing Naming Convention Standardization...\n');

// Apply import updates for files that exist
applyImportUpdates();

// Show manual steps
showManualSteps();

console.log('\n📊 Completion Status:\n');
console.log('✅ Import updates applied to existing files');
console.log('⏳ File renaming needs to be done manually');
console.log('⏳ Final testing required after file renames');

console.log('\n🎉 Ready for Final Phase!');
console.log('After file renaming, the naming convention standardization will be 100% complete!');

module.exports = {
  fileRenames,
  importUpdates,
  applyImportUpdates,
  fileExists
};
