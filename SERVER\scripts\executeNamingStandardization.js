// Execute naming convention standardization across the codebase
const fs = require('fs');
const path = require('path');

console.log('🚀 Executing Naming Convention Standardization\n');

// Define all the updates needed
const updates = {
  // Files that need content updates (not renaming)
  contentUpdates: [
    {
      file: 'controllers/mooeController.js',
      replacements: [
        { from: 'MOOEProposal', to: 'MooeProposal' },
        { from: 'ChartOfAccounts', to: 'ChartOfAccounts' } // Already correct
      ]
    },
    {
      file: 'controllers/proposalController.js', 
      replacements: [
        { from: 'MooeProposal', to: 'MooeProposal' }, // Check if needs update
        { from: 'const IncomeSubcategory', to: 'const IncomeSubcategory' } // Check current naming
      ]
    },
    {
      file: 'controllers/budgetManagerOrgController.js',
      replacements: [
        { from: 'MooeProposal', to: 'MooeProposal' },
        { from: 'PersonnelServices', to: 'PersonnelServices' }
      ]
    },
    {
      file: 'routers/budgetManagerOrgRoutes.js',
      replacements: [
        { from: 'MooeProposal', to: 'MooeProposal' },
        { from: 'PersonnelServices', to: 'PersonnelServices' }
      ]
    },
    {
      file: 'index.js',
      replacements: [
        { from: 'const budgetManagerOrgRouter', to: 'const budgetManagerOrgRoutes' },
        { from: 'const rataRouter', to: 'const rataRoutes' },
        { from: 'const ps_annexes', to: 'const personnelServicesAnnexesRoutes' },
        { from: 'app.use(budgetManagerOrgRouter)', to: 'app.use(budgetManagerOrgRoutes)' },
        { from: 'app.use(rataRouter)', to: 'app.use(rataRoutes)' },
        { from: 'app.use(ps_annexes)', to: 'app.use(personnelServicesAnnexesRoutes)' }
      ]
    }
  ],

  // Function name updates
  functionUpdates: [
    {
      file: 'controllers/ps_annexes_controller.js',
      replacements: [
        { from: 'exports.addPS_Annex', to: 'exports.addPersonnelServicesAnnex' },
        { from: 'exports.editPS_Annex', to: 'exports.editPersonnelServicesAnnex' },
        { from: 'exports.deletePS_Annex', to: 'exports.deletePersonnelServicesAnnex' }
      ]
    }
  ],

  // Model updates needed
  modelUpdates: [
    {
      file: 'models/childrenAllowance.js',
      newModelName: 'ChildrenAllowance',
      schemaName: 'childrenAllowanceSchema'
    },
    {
      file: 'models/employeeCourtAppearance.js', 
      newModelName: 'EmployeeCourtAppearance',
      schemaName: 'employeeCourtAppearanceSchema'
    },
    {
      file: 'models/loyaltyPay.js',
      newModelName: 'LoyaltyPay', 
      schemaName: 'loyaltyPaySchema'
    },
    {
      file: 'models/mealAllowance.js',
      newModelName: 'MealAllowance',
      schemaName: 'mealAllowanceSchema'
    },
    {
      file: 'models/overtimePay.js',
      newModelName: 'OvertimePay',
      schemaName: 'overtimePaySchema'
    },
    {
      file: 'models/subsistenceAllowanceMDS.js',
      newModelName: 'SubsistenceAllowanceMDS',
      schemaName: 'subsistenceAllowanceMDSSchema'
    },
    {
      file: 'models/subsistenceAllowanceST.js',
      newModelName: 'SubsistenceAllowanceST', 
      schemaName: 'subsistenceAllowanceSTSchema'
    }
  ]
};

// Function to read file content
function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.log(`❌ Could not read ${filePath}: ${error.message}`);
    return null;
  }
}

// Function to write file content
function writeFile(filePath, content) {
  try {
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  } catch (error) {
    console.log(`❌ Could not write ${filePath}: ${error.message}`);
    return false;
  }
}

// Function to apply content updates
function applyContentUpdates() {
  console.log('📝 Applying Content Updates:\n');
  
  updates.contentUpdates.forEach(update => {
    console.log(`🔄 Updating ${update.file}...`);
    
    const content = readFile(update.file);
    if (!content) return;
    
    let updatedContent = content;
    let changesMade = false;
    
    update.replacements.forEach(replacement => {
      const regex = new RegExp(replacement.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      const matches = updatedContent.match(regex);
      
      if (matches && matches.length > 0) {
        updatedContent = updatedContent.replace(regex, replacement.to);
        changesMade = true;
        console.log(`   ✅ ${replacement.from} → ${replacement.to} (${matches.length} occurrences)`);
      }
    });
    
    if (changesMade) {
      if (writeFile(update.file, updatedContent)) {
        console.log(`   💾 ${update.file} updated successfully`);
      }
    } else {
      console.log(`   ℹ️  No changes needed in ${update.file}`);
    }
    console.log('');
  });
}

// Function to apply function name updates
function applyFunctionUpdates() {
  console.log('🔧 Applying Function Updates:\n');
  
  updates.functionUpdates.forEach(update => {
    console.log(`🔄 Updating functions in ${update.file}...`);
    
    const content = readFile(update.file);
    if (!content) return;
    
    let updatedContent = content;
    let changesMade = false;
    
    update.replacements.forEach(replacement => {
      if (updatedContent.includes(replacement.from)) {
        updatedContent = updatedContent.replace(new RegExp(replacement.from, 'g'), replacement.to);
        changesMade = true;
        console.log(`   ✅ ${replacement.from} → ${replacement.to}`);
      }
    });
    
    if (changesMade) {
      if (writeFile(update.file, updatedContent)) {
        console.log(`   💾 ${update.file} updated successfully`);
      }
    } else {
      console.log(`   ℹ️  No function updates needed in ${update.file}`);
    }
    console.log('');
  });
}

// Function to show what model updates are needed
function showModelUpdates() {
  console.log('📊 Model Updates Needed:\n');
  
  updates.modelUpdates.forEach(update => {
    console.log(`📁 ${update.file}:`);
    console.log(`   Model Name: → ${update.newModelName}`);
    console.log(`   Schema Name: → ${update.schemaName}`);
    console.log('');
  });
}

// Function to show file rename mappings
function showFileRenameMappings() {
  console.log('📋 File Rename Mappings:\n');
  
  const renameMappings = [
    'controllers/BudgetManagerOrgController.js → controllers/budgetManagerOrgController.js',
    'controllers/RATAController.js → controllers/rataController.js', 
    'controllers/COSPersonnelController.js → controllers/cosPersonnelController.js',
    'controllers/ps_annexes_controller.js → controllers/personnelServicesAnnexesController.js',
    'routers/BudgetManagerOrgRoutes.js → routers/budgetManagerOrgRoutes.js',
    'routers/RATARoutes.js → routers/rataRoutes.js',
    'routers/COSPersonnel_router.js → routers/cosPersonnelRoutes.js',
    'routers/ps_annexes.js → routers/personnelServicesAnnexesRoutes.js',
    'middleware/check_token.js → middleware/checkToken.js'
  ];
  
  renameMappings.forEach(mapping => {
    console.log(`📁 ${mapping}`);
  });
  console.log('');
}

// Execute the standardization
console.log('🎯 Starting Naming Convention Standardization...\n');

// Show what will be done
showFileRenameMappings();
showModelUpdates();

// Apply updates
applyContentUpdates();
applyFunctionUpdates();

console.log('📊 Summary:\n');
console.log('✅ Content updates applied to existing files');
console.log('✅ Function names standardized');
console.log('⏳ File renaming needs to be done manually or with IDE');
console.log('⏳ Model schema updates need manual review');
console.log('⏳ Import statement updates needed after file renames');

console.log('\n🎯 Next Steps:');
console.log('1. Review the changes made to files');
console.log('2. Rename files according to the mapping shown above');
console.log('3. Update import statements in affected files');
console.log('4. Test functionality after changes');
console.log('5. Update client-side references if needed');

console.log('\n🎉 Naming Convention Standardization Progress Complete!');

module.exports = {
  updates,
  applyContentUpdates,
  applyFunctionUpdates,
  showModelUpdates,
  showFileRenameMappings
};
