// Implementation script for naming convention standardization
const fs = require('fs');
const path = require('path');

console.log('🔧 Implementing Naming Convention Standardization\n');

// Phase 1: Most Critical Files
const phase1Files = [
  // Models that need renaming
  {
    type: 'model',
    oldPath: 'models/chartOfAccounts.js',
    newPath: 'models/ChartOfAccounts.js',
    oldModelName: 'chart_of_account',
    newModelName: 'ChartOfAccounts'
  },
  {
    type: 'model', 
    oldPath: 'models/mooeProposals.js',
    newPath: 'models/MooeProposals.js',
    oldModelName: 'MOOEProposal',
    newModelName: 'MooeProposal'
  },
  
  // Controllers that need renaming
  {
    type: 'controller',
    oldPath: 'controllers/BudgetManagerOrgController.js',
    newPath: 'controllers/budgetManagerOrgController.js'
  },
  {
    type: 'controller',
    oldPath: 'controllers/RATAController.js', 
    newPath: 'controllers/rataController.js'
  },
  
  // Routes that need renaming
  {
    type: 'route',
    oldPath: 'routers/BudgetManagerOrgRoutes.js',
    newPath: 'routers/budgetManagerOrgRoutes.js',
    oldRouterName: 'budgetManagerOrgRouter',
    newRouterName: 'budgetManagerOrgRoutes'
  },
  {
    type: 'route',
    oldPath: 'routers/RATARoutes.js',
    newPath: 'routers/rataRoutes.js',
    oldRouterName: 'rataRouter',
    newRouterName: 'rataRoutes'
  }
];

// Function to check if file exists
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

// Function to get file content
function getFileContent(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`❌ Error reading file ${filePath}:`, error.message);
    return null;
  }
}

// Function to update import statements in a file
function updateImportStatements(content, mappings) {
  let updatedContent = content;
  
  mappings.forEach(mapping => {
    // Update require statements
    const requireRegex = new RegExp(`require\\(['"]([^'"]*${mapping.oldPath})['"]\\)`, 'g');
    updatedContent = updatedContent.replace(requireRegex, `require('$1'.replace('${mapping.oldPath}', '${mapping.newPath}'))`);
    
    // Update model names in content
    if (mapping.oldModelName && mapping.newModelName) {
      const modelRegex = new RegExp(`\\b${mapping.oldModelName}\\b`, 'g');
      updatedContent = updatedContent.replace(modelRegex, mapping.newModelName);
    }
  });
  
  return updatedContent;
}

// Display implementation plan
console.log('📋 Phase 1 Implementation Plan:\n');

phase1Files.forEach((file, index) => {
  console.log(`${index + 1}. ${file.type.toUpperCase()}: ${file.oldPath} → ${file.newPath}`);
  if (file.oldModelName) {
    console.log(`   Model: ${file.oldModelName} → ${file.newModelName}`);
  }
  if (file.oldRouterName) {
    console.log(`   Router: ${file.oldRouterName} → ${file.newRouterName}`);
  }
  console.log('');
});

// Check which files exist
console.log('📁 File Existence Check:\n');

phase1Files.forEach(file => {
  const exists = fileExists(file.oldPath);
  const status = exists ? '✅' : '❌';
  console.log(`${status} ${file.oldPath} ${exists ? 'exists' : 'not found'}`);
});

console.log('\n🔄 Files that need import updates:\n');

// Files that import the renamed files
const filesToUpdate = [
  'index.js',
  'controllers/mooeController.js',
  'controllers/proposalController.js',
  'controllers/budgetManagerOrgController.js',
  'routers/budgetManagerOrgRoutes.js',
  'routers/rataRoutes.js'
];

filesToUpdate.forEach(file => {
  const exists = fileExists(file);
  const status = exists ? '✅' : '❌';
  console.log(`${status} ${file} ${exists ? 'needs update' : 'not found'}`);
});

console.log('\n📝 Manual Steps Required:\n');

const manualSteps = [
  '1. Rename files using file system or IDE',
  '2. Update all require/import statements',
  '3. Update variable and function names',
  '4. Update index.js route registrations',
  '5. Update client-side API calls',
  '6. Test functionality after each change',
  '7. Commit changes incrementally'
];

manualSteps.forEach(step => console.log(`   ${step}`));

console.log('\n🎯 Recommended Implementation Order:\n');

const implementationOrder = [
  '1. Models first (least dependencies)',
  '2. Controllers second (depend on models)',
  '3. Routes third (depend on controllers)',
  '4. Middleware and utils',
  '5. Update index.js registrations',
  '6. Update client-side references',
  '7. Test and validate'
];

implementationOrder.forEach(step => console.log(`   ${step}`));

console.log('\n⚠️  Critical Considerations:\n');

const considerations = [
  '• Database collection names should NOT change',
  '• API endpoints should remain the same for client compatibility',
  '• Update imports in ALL files that reference renamed files',
  '• Test thoroughly after each phase',
  '• Consider using IDE refactoring tools for safety',
  '• Keep backup of original files',
  '• Update documentation and comments'
];

considerations.forEach(consideration => console.log(`   ${consideration}`));

console.log('\n🔧 Example Updates Needed:\n');

// Show example of what needs to be updated
console.log('In index.js:');
console.log('❌ const budgetManagerOrgRouter = require("./routers/BudgetManagerOrgRoutes");');
console.log('✅ const budgetManagerOrgRoutes = require("./routers/budgetManagerOrgRoutes");');
console.log('');

console.log('In controllers:');
console.log('❌ const MOOEProposal = require("../models/mooeProposals");');
console.log('✅ const MooeProposal = require("../models/MooeProposals");');
console.log('');

console.log('In models:');
console.log('❌ module.exports = mongoose.model("chart_of_account", chartofAccountSchema);');
console.log('✅ module.exports = mongoose.model("ChartOfAccounts", chartOfAccountsSchema);');

console.log('\n🚀 Ready to implement Phase 1 of naming standardization!');
console.log('   📊 6 critical files identified for Phase 1');
console.log('   🔄 Multiple dependent files need updates');
console.log('   ⚡ Systematic approach recommended');

module.exports = {
  phase1Files,
  filesToUpdate,
  fileExists,
  getFileContent,
  updateImportStatements
};
