const mongoose = require('mongoose');
require('dotenv').config();

const Category = require('../models/Category');
const ChartOfAccounts = require('../models/chartOfAccounts');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URL || 'mongodb://localhost:27017/budget-fmis')
  .then(() => {
    console.log('Connected to MongoDB');
    seedCapitalOutlayData();
  })
  .catch(err => {
    console.error('Error connecting to MongoDB:', err);
    process.exit(1);
  });

async function seedCapitalOutlayData() {
  try {
    console.log('Starting Capital Outlay data seeding...');

    // 1. Seed Categories with proper subline items
    const capitalOutlayCategories = [
      {
        categoryName: "Infrastructure Outlay",
        description: "Infrastructure development projects",
        sublineItems: ["Infrastructure Outlay"]
      },
      {
        categoryName: "Building and Other Structures",
        description: "Building construction and structural projects",
        sublineItems: ["Building and Other Structures"]
      },
      {
        categoryName: "Machinery and Equipment Outlay",
        description: "Purchase of machinery and equipment",
        sublineItems: ["Machinery and Equipment Outlay"]
      },
      {
        categoryName: "Transportation Equipment Outlay",
        description: "Purchase of transportation equipment",
        sublineItems: ["Transportation Equipment Outlay"]
      },
      {
        categoryName: "Furniture, Fixtures and Books Outlay",
        description: "Purchase of furniture, fixtures and books",
        sublineItems: ["Furniture, Fixtures and Books Outlay"]
      }
    ];

    // Insert or update categories
    for (const categoryData of capitalOutlayCategories) {
      const existingCategory = await Category.findOne({ categoryName: categoryData.categoryName });
      
      if (existingCategory) {
        // Update existing category with subline items
        await Category.findByIdAndUpdate(existingCategory._id, {
          sublineItems: categoryData.sublineItems,
          description: categoryData.description
        });
        console.log(`Updated category: ${categoryData.categoryName}`);
      } else {
        // Create new category
        await Category.create(categoryData);
        console.log(`Created category: ${categoryData.categoryName}`);
      }
    }

    // 2. Seed Chart of Accounts for Capital Outlay
    const existingCoaEntries = await ChartOfAccounts.find({ 
      uacsCode: { $regex: /^5-01/ } 
    });

    if (existingCoaEntries.length === 0) {
      console.log('Seeding Capital Outlay Chart of Accounts...');

      const capitalOutlayChartOfAccounts = [
        // Infrastructure Outlay
        { sublineItem: "Infrastructure Outlay", accountingTitle: "Infrastructure Outlay - Roads and Bridges", uacsCode: "5-01-01-010" },
        { sublineItem: "Infrastructure Outlay", accountingTitle: "Infrastructure Outlay - Flood Control Systems", uacsCode: "5-01-01-020" },
        { sublineItem: "Infrastructure Outlay", accountingTitle: "Infrastructure Outlay - Water Supply Systems", uacsCode: "5-01-01-030" },
        { sublineItem: "Infrastructure Outlay", accountingTitle: "Infrastructure Outlay - Irrigation Systems", uacsCode: "5-01-01-040" },
        { sublineItem: "Infrastructure Outlay", accountingTitle: "Infrastructure Outlay - Other Infrastructure", uacsCode: "5-01-01-990" },

        // Building and Other Structures
        { sublineItem: "Building and Other Structures", accountingTitle: "Buildings", uacsCode: "5-01-02-010" },
        { sublineItem: "Building and Other Structures", accountingTitle: "School Buildings", uacsCode: "5-01-02-020" },
        { sublineItem: "Building and Other Structures", accountingTitle: "Hospitals and Health Centers", uacsCode: "5-01-02-030" },
        { sublineItem: "Building and Other Structures", accountingTitle: "Markets", uacsCode: "5-01-02-040" },
        { sublineItem: "Building and Other Structures", accountingTitle: "Slaughterhouses", uacsCode: "5-01-02-050" },
        { sublineItem: "Building and Other Structures", accountingTitle: "Hostels and Dormitories", uacsCode: "5-01-02-060" },
        { sublineItem: "Building and Other Structures", accountingTitle: "Other Structures", uacsCode: "5-01-02-990" },

        // Buildings and Other Structures (alternative naming)
        { sublineItem: "Buildings and Other Structures", accountingTitle: "Buildings", uacsCode: "5-01-02-010" },
        { sublineItem: "Buildings and Other Structures", accountingTitle: "School Buildings", uacsCode: "5-01-02-020" },
        { sublineItem: "Buildings and Other Structures", accountingTitle: "Hospitals and Health Centers", uacsCode: "5-01-02-030" },
        { sublineItem: "Buildings and Other Structures", accountingTitle: "Markets", uacsCode: "5-01-02-040" },
        { sublineItem: "Buildings and Other Structures", accountingTitle: "Slaughterhouses", uacsCode: "5-01-02-050" },
        { sublineItem: "Buildings and Other Structures", accountingTitle: "Hostels and Dormitories", uacsCode: "5-01-02-060" },
        { sublineItem: "Buildings and Other Structures", accountingTitle: "Other Structures", uacsCode: "5-01-02-990" },

        // Repairs and Maintenance
        { sublineItem: "Repairs and Maintenance", accountingTitle: "Repairs and Maintenance - Buildings and Other Structures", uacsCode: "5-02-13-010" },
        { sublineItem: "Repairs and Maintenance", accountingTitle: "Repairs and Maintenance - Machinery and Equipment", uacsCode: "5-02-13-020" },
        { sublineItem: "Repairs and Maintenance", accountingTitle: "Repairs and Maintenance - Transportation Equipment", uacsCode: "5-02-13-030" },
        { sublineItem: "Repairs and Maintenance", accountingTitle: "Repairs and Maintenance - Other Property, Plant and Equipment", uacsCode: "5-02-13-990" },

        // Machinery and Equipment Outlay
        { sublineItem: "Machinery and Equipment Outlay", accountingTitle: "Office Equipment", uacsCode: "5-01-03-010" },
        { sublineItem: "Machinery and Equipment Outlay", accountingTitle: "Information and Communications Technology Equipment", uacsCode: "5-01-03-020" },
        { sublineItem: "Machinery and Equipment Outlay", accountingTitle: "Agricultural and Forestry Equipment", uacsCode: "5-01-03-030" },
        { sublineItem: "Machinery and Equipment Outlay", accountingTitle: "Military, Police and Security Equipment", uacsCode: "5-01-03-040" },
        { sublineItem: "Machinery and Equipment Outlay", accountingTitle: "Medical Equipment", uacsCode: "5-01-03-050" },
        { sublineItem: "Machinery and Equipment Outlay", accountingTitle: "Printing Equipment", uacsCode: "5-01-03-060" },
        { sublineItem: "Machinery and Equipment Outlay", accountingTitle: "Sports Equipment", uacsCode: "5-01-03-070" },
        { sublineItem: "Machinery and Equipment Outlay", accountingTitle: "Technical and Scientific Equipment", uacsCode: "5-01-03-080" },
        { sublineItem: "Machinery and Equipment Outlay", accountingTitle: "Other Machinery and Equipment", uacsCode: "5-01-03-990" },

        // Machinery and Equipment (alternative naming)
        { sublineItem: "Machinery and Equipment", accountingTitle: "Office Equipment", uacsCode: "5-01-03-010" },
        { sublineItem: "Machinery and Equipment", accountingTitle: "Information and Communications Technology Equipment", uacsCode: "5-01-03-020" },
        { sublineItem: "Machinery and Equipment", accountingTitle: "Agricultural and Forestry Equipment", uacsCode: "5-01-03-030" },
        { sublineItem: "Machinery and Equipment", accountingTitle: "Military, Police and Security Equipment", uacsCode: "5-01-03-040" },
        { sublineItem: "Machinery and Equipment", accountingTitle: "Medical Equipment", uacsCode: "5-01-03-050" },
        { sublineItem: "Machinery and Equipment", accountingTitle: "Printing Equipment", uacsCode: "5-01-03-060" },
        { sublineItem: "Machinery and Equipment", accountingTitle: "Sports Equipment", uacsCode: "5-01-03-070" },
        { sublineItem: "Machinery and Equipment", accountingTitle: "Technical and Scientific Equipment", uacsCode: "5-01-03-080" },
        { sublineItem: "Machinery and Equipment", accountingTitle: "Other Machinery and Equipment", uacsCode: "5-01-03-990" },

        // Transportation Equipment Outlay
        { sublineItem: "Transportation Equipment Outlay", accountingTitle: "Motor Vehicles", uacsCode: "5-01-04-010" },
        { sublineItem: "Transportation Equipment Outlay", accountingTitle: "Aircraft", uacsCode: "5-01-04-020" },
        { sublineItem: "Transportation Equipment Outlay", accountingTitle: "Watercrafts", uacsCode: "5-01-04-030" },
        { sublineItem: "Transportation Equipment Outlay", accountingTitle: "Other Transportation Equipment", uacsCode: "5-01-04-990" },

        // Furniture, Fixtures and Books Outlay
        { sublineItem: "Furniture, Fixtures and Books Outlay", accountingTitle: "Furniture and Fixtures", uacsCode: "5-01-05-010" },
        { sublineItem: "Furniture, Fixtures and Books Outlay", accountingTitle: "Books", uacsCode: "5-01-05-020" },

        // Furniture Fixture and Books (alternative naming)
        { sublineItem: "Furniture Fixture and Books", accountingTitle: "Furniture and Fixtures", uacsCode: "5-01-05-010" },
        { sublineItem: "Furniture Fixture and Books", accountingTitle: "Books", uacsCode: "5-01-05-020" },

        // Land
        { sublineItem: "Land", accountingTitle: "Land", uacsCode: "5-01-06-010" },
        { sublineItem: "Land", accountingTitle: "Land Rights", uacsCode: "5-01-06-020" },

        // Land Improvements
        { sublineItem: "Land Improvements", accountingTitle: "Land Improvements", uacsCode: "5-01-07-010" },
        { sublineItem: "Land Improvements", accountingTitle: "Site Development", uacsCode: "5-01-07-020" },
        { sublineItem: "Land Improvements", accountingTitle: "Landscaping", uacsCode: "5-01-07-030" }
      ];

      await ChartOfAccounts.insertMany(capitalOutlayChartOfAccounts);
      console.log(`Seeded ${capitalOutlayChartOfAccounts.length} Capital Outlay chart of accounts entries`);
    } else {
      console.log(`Capital Outlay chart of accounts already exists (${existingCoaEntries.length} entries)`);
    }

    console.log('Capital Outlay data seeding completed successfully!');
    
    // Display summary
    const categories = await Category.find({ categoryName: { $regex: /outlay|infrastructure|building|machinery|transportation|furniture/i } });
    const coaEntries = await ChartOfAccounts.find({ uacsCode: { $regex: /^5-01/ } });
    
    console.log('\n=== SUMMARY ===');
    console.log(`Capital Outlay Categories: ${categories.length}`);
    categories.forEach(cat => {
      console.log(`  - ${cat.categoryName}: ${cat.sublineItems?.length || 0} subline items`);
    });
    console.log(`Capital Outlay Chart of Accounts: ${coaEntries.length} entries`);
    
    mongoose.disconnect();
  } catch (error) {
    console.error('Error seeding Capital Outlay data:', error);
    mongoose.disconnect();
    process.exit(1);
  }
}
