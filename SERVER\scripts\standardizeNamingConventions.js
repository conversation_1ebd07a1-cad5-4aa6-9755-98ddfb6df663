// Script to standardize naming conventions across the entire codebase
const fs = require('fs');
const path = require('path');

console.log('🔧 Standardizing Naming Conventions\n');

// Define naming convention mappings
const fileRenameMappings = {
  // Controllers: camelCase + Controller.js
  'controllers/ps_annexes_controller.js': 'controllers/personnelServicesAnnexesController.js',
  'controllers/BudgetManagerOrgController.js': 'controllers/budgetManagerOrgController.js',
  'controllers/UserOrganizationalAssignmentController.js': 'controllers/userOrganizationalAssignmentController.js',
  'controllers/UserRegionAssignmentController.js': 'controllers/userRegionAssignmentController.js',
  'controllers/RATAController.js': 'controllers/rataController.js',
  'controllers/COSPersonnelController.js': 'controllers/cosPersonnelController.js',
  'controllers/DepartmentController.js': 'controllers/departmentController.js',
  'controllers/EmployeeListController.js': 'controllers/employeeListController.js',
  'controllers/IncomeCategoryController.js': 'controllers/incomeCategoryController.js',
  'controllers/IncomeController.js': 'controllers/incomeController.js',
  'controllers/IncomeSubcategoryController.js': 'controllers/incomeSubcategoryController.js',
  'controllers/PositionTitleController.js': 'controllers/positionTitleController.js',
  'controllers/RegionController.js': 'controllers/regionController.js',
  'controllers/SpecialCounselAllowanceController.js': 'controllers/specialCounselAllowanceController.js',
  'controllers/StatusController.js': 'controllers/statusController.js',
  
  // Models: PascalCase.js
  'models/chartOfAccounts.js': 'models/ChartOfAccounts.js',
  'models/childrenAllowance.js': 'models/ChildrenAllowance.js',
  'models/employeeCourtAppearance.js': 'models/EmployeeCourtAppearance.js',
  'models/employee_master.js': 'models/EmployeeMaster.js',
  'models/loyaltyPay.js': 'models/LoyaltyPay.js',
  'models/mealAllowance.js': 'models/MealAllowance.js',
  'models/mooeProposals.js': 'models/MooeProposals.js',
  'models/overtimePay.js': 'models/OvertimePay.js',
  'models/setup.js': 'models/Setup.js',
  'models/subsistenceAllowanceMDS.js': 'models/SubsistenceAllowanceMDS.js',
  'models/subsistenceAllowanceST.js': 'models/SubsistenceAllowanceST.js',
  'models/test.js': 'models/Test.js',
  'models/PS_Annexes.js': 'models/PersonnelServicesAnnexes.js',
  
  // Routes: camelCase + Routes.js
  'routers/RATARoutes.js': 'routers/rataRoutes.js',
  'routers/BudgetManagerOrgRoutes.js': 'routers/budgetManagerOrgRoutes.js',
  'routers/UserOrganizationalAssignmentRoutes.js': 'routers/userOrganizationalAssignmentRoutes.js',
  'routers/UserRegionAssignmentRoutes.js': 'routers/userRegionAssignmentRoutes.js',
  'routers/COSPersonnel_router.js': 'routers/cosPersonnelRoutes.js',
  'routers/RegionRoutes.js': 'routers/regionRoutes.js',
  'routers/SpecialCounselAllowanceRoutes.js': 'routers/specialCounselAllowanceRoutes.js',
  'routers/ps_annexes.js': 'routers/personnelServicesAnnexesRoutes.js',
  'routers/Department.js': 'routers/departmentRoutes.js',
  'routers/PositionTitle.js': 'routers/positionTitleRoutes.js',
  'routers/Salary_router.js': 'routers/salaryRoutes.js',
  'routers/Status.js': 'routers/statusRoutes.js',
  'routers/EmployeeList_router.js': 'routers/employeeListRoutes.js',
  'routers/employee_master_router.js': 'routers/employeeMasterRoutes.js',
  'routers/personnel_params_router.js': 'routers/personnelParamsRoutes.js',
  'routers/loyaltyPay_router.js': 'routers/loyaltyPayRoutes.js',
  'routers/income_category_router.js': 'routers/incomeCategoryRoutes.js',
  'routers/income_subcategory_router.js': 'routers/incomeSubcategoryRoutes.js',
  'routers/AccountingEntryList_router.js': 'routers/accountingEntryListRoutes.js',
  'routers/personnelServices_router.js': 'routers/personnelServicesRoutes.js',
  'routers/personnelServices_router_simplified.js': 'routers/personnelServicesSimplifiedRoutes.js',
  'routers/test_router.js': 'routers/testRoutes.js',
  'routers/reports.routes.js': 'routers/reportsRoutes.js',
  
  // Middleware: camelCase.js
  'middleware/check_token.js': 'middleware/checkToken.js',
  
  // Utils: camelCase.js
  'utils/controller_get_process.js': 'utils/controllerGetProcess.js',
  'utils/date-related.js': 'utils/dateRelated.js'
};

// Variable and function name mappings
const variableRenameMappings = {
  // Variables
  'ps_annexes': 'personnelServicesAnnexes',
  'PS_Annexes': 'PersonnelServicesAnnexes',
  'getAllPS_Annexes': 'getAllPersonnelServicesAnnexes',
  'createPS_Annexes': 'createPersonnelServicesAnnexes',
  'updatePS_Annexes': 'updatePersonnelServicesAnnexes',
  'deletePS_Annexes': 'deletePersonnelServicesAnnexes',
  'MOOEProposal': 'MooeProposal',
  'chartofAccountSchema': 'chartOfAccountsSchema',
  'chart_of_account': 'ChartOfAccounts',
  'cosPersonnelRouter': 'cosPersonnelRoutes',
  'rataRouter': 'rataRoutes'
};

// Display the standardization plan
console.log('📋 File Rename Plan:\n');

Object.entries(fileRenameMappings).forEach(([oldPath, newPath]) => {
  console.log(`📁 ${oldPath} → ${newPath}`);
});

console.log('\n📝 Variable Rename Plan:\n');

Object.entries(variableRenameMappings).forEach(([oldName, newName]) => {
  console.log(`🔤 ${oldName} → ${newName}`);
});

console.log('\n🎯 Naming Convention Standards:\n');

const standards = {
  'Controllers': 'camelCase + Controller.js (e.g., personnelServicesController.js)',
  'Models': 'PascalCase.js (e.g., PersonnelServices.js)',
  'Routes': 'camelCase + Routes.js (e.g., personnelServicesRoutes.js)',
  'Components': 'PascalCase.jsx (e.g., PersonnelServicesPage.jsx)',
  'Variables': 'camelCase (e.g., personnelServicesData)',
  'Functions': 'camelCase (e.g., getAllPersonnelServices)',
  'Constants': 'UPPER_SNAKE_CASE (e.g., PERMISSION_LEVELS)',
  'Middleware': 'camelCase.js (e.g., checkToken.js)',
  'Utils': 'camelCase.js (e.g., dateRelated.js)'
};

Object.entries(standards).forEach(([category, standard]) => {
  console.log(`✅ ${category}: ${standard}`);
});

console.log('\n🔧 Implementation Steps:\n');

const steps = [
  '1. Rename files according to conventions',
  '2. Update all import/require statements',
  '3. Update variable and function names',
  '4. Update route registrations in index.js',
  '5. Update client-side imports and references',
  '6. Test all functionality after changes',
  '7. Update documentation to reflect new naming'
];

steps.forEach(step => console.log(`   ${step}`));

console.log('\n📊 Benefits of Standardization:\n');

const benefits = [
  '✅ Consistent and predictable file structure',
  '✅ Easier navigation and code discovery',
  '✅ Better IDE autocomplete and IntelliSense',
  '✅ Reduced cognitive load for developers',
  '✅ Professional and maintainable codebase',
  '✅ Easier onboarding for new team members',
  '✅ Better code organization and readability'
];

benefits.forEach(benefit => console.log(`   ${benefit}`));

console.log('\n🚨 Important Notes:\n');

const notes = [
  '⚠️  This is a major refactoring that will affect many files',
  '⚠️  All imports and references need to be updated',
  '⚠️  Database model names should remain consistent',
  '⚠️  Client-side routes and API calls need updates',
  '⚠️  Thorough testing required after implementation',
  '⚠️  Consider doing this in phases to minimize disruption'
];

notes.forEach(note => console.log(`   ${note}`));

console.log('\n🎉 Ready to Standardize Naming Conventions!');

module.exports = {
  fileRenameMappings,
  variableRenameMappings,
  standards
};
