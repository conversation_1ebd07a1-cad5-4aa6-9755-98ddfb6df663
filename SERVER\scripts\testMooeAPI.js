// Test script to debug MOOE API issues
const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Settings = require('../models/Setup');
const ChartOfAccounts = require('../models/ChartOfAccounts');
const MooeProposal = require('../models/mooeProposals');
const COSPersonnel = require('../models/COSPersonnel');

async function testMooeAPI() {
  try {
    console.log('🔧 Testing MOOE API Components...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB\n');
    
    // 1. Check Settings
    console.log('1. Checking Settings...');
    const activeSettings = await Settings.findOne({ isActive: true });
    console.log('Active Settings:', activeSettings);
    
    if (!activeSettings) {
      console.log('❌ No active settings found!');
      
      // Check all settings
      const allSettings = await Settings.find({});
      console.log('All Settings:', allSettings.length, 'records');
      if (allSettings.length > 0) {
        console.log('First setting:', allSettings[0]);
      }
    } else {
      console.log('✅ Active fiscal year:', activeSettings.fiscalYear);
    }
    
    // 2. Check Chart of Accounts
    console.log('\n2. Checking Chart of Accounts...');
    const chartOfAccounts = await ChartOfAccounts.find({
      accountClass: "Expense",
      lineItem: "Maintenance and Other Operating Expenses"
    }).lean();
    console.log('✅ Chart of Accounts found:', chartOfAccounts.length, 'records');
    if (chartOfAccounts.length > 0) {
      console.log('Sample COA:', chartOfAccounts[0]);
    }
    
    // 3. Check MOOE Proposals
    console.log('\n3. Checking MOOE Proposals...');
    const allMooeProposals = await MooeProposal.find({}).lean();
    console.log('✅ Total MOOE Proposals:', allMooeProposals.length);
    
    if (activeSettings) {
      const mooeProposals = await MooeProposal.find({ 
        fiscalYear: activeSettings.fiscalYear 
      }).lean();
      console.log('✅ MOOE Proposals for active fiscal year:', mooeProposals.length);
      if (mooeProposals.length > 0) {
        console.log('Sample MOOE:', mooeProposals[0]);
      }
    }
    
    // 4. Check COS Personnel
    console.log('\n4. Checking COS Personnel...');
    const allCOSPersonnel = await COSPersonnel.find({}).lean();
    console.log('✅ Total COS Personnel:', allCOSPersonnel.length);
    
    if (activeSettings) {
      const cosPersonnels = await COSPersonnel.find({
        statusOfAppointment: "COS",
        fiscalYear: activeSettings.fiscalYear
      }).lean();
      console.log('✅ COS Personnel for active fiscal year:', cosPersonnels.length);
    }
    
    // 5. Simulate the getMOOEData function
    console.log('\n5. Simulating getMOOEData function...');
    
    if (!activeSettings || activeSettings.fiscalYear == null) {
      console.log('❌ Cannot simulate - no active fiscal year');
      
      // Try to create a default setting
      console.log('Creating default active setting...');
      const defaultSetting = new Settings({
        fiscalYear: '2026',
        budgetType: 'Initial',
        isActive: true,
        processBy: 'system',
        processDate: new Date()
      });
      await defaultSetting.save();
      console.log('✅ Default setting created');
      
      // Retry
      const newActiveSettings = await Settings.findOne({ isActive: true });
      console.log('New active settings:', newActiveSettings);
    } else {
      const activeFiscalYear = activeSettings.fiscalYear;
      
      // Fetch all data like the real function
      const [chartOfAccounts2, mooeProposals2, cosPersonnels2] = await Promise.all([
        ChartOfAccounts.find({
          accountClass: "Expense",
          lineItem: "Maintenance and Other Operating Expenses"
        }).lean(),
        
        MooeProposal.find({ fiscalYear: activeFiscalYear }).lean(),
        
        COSPersonnel.find({
          statusOfAppointment: "COS",
          fiscalYear: activeFiscalYear
        }).lean()
      ]);
      
      console.log('Data fetched successfully:');
      console.log('- Chart of Accounts:', chartOfAccounts2.length);
      console.log('- MOOE Proposals:', mooeProposals2.length);
      console.log('- COS Personnel:', cosPersonnels2.length);
      
      // Compute total COS personnel amount
      const totalPersonnelAmount = cosPersonnels2
        .reduce((sum, p) => sum + (p.Total || 0), 0);
      console.log('- Total Personnel Amount:', totalPersonnelAmount);
      
      // Build lookup for MOOE amounts
      const mooeMap = Object.fromEntries(
        mooeProposals2.map(row => [row.uacsCode, row])
      );
      console.log('- MOOE Map keys:', Object.keys(mooeMap));
      
      // Determine submission status
      const status = mooeProposals2.length > 0
        ? mooeProposals2[0].status
        : "Not Submitted";
      console.log('- Status:', status);
      
      // Assemble entries array
      const entries = chartOfAccounts2.map(row => {
        const proposal = mooeMap[row.uacsCode];
        return {
          id: proposal?._id?.toString() || null,
          sublineItem: row.sublineItem,
          accountingTitle: row.accountingTitle,
          uacsCode: row.uacsCode,
          income: proposal?.income?.toString() || "0",
          subsidy: proposal?.subsidy?.toString() || "0",
          amount: row.uacsCode === "5-02-99-990"
            ? totalPersonnelAmount.toString()
            : (proposal?.amount?.toString() || "0")
        };
      });
      
      console.log('✅ Entries generated:', entries.length);
      console.log('Sample entry:', entries[0]);
      
      const result = {
        entries,
        status,
        settings: {
          fiscalYear: activeFiscalYear,
          budgetType: activeSettings.budgetType
        }
      };
      
      console.log('\n🎉 API Response would be:');
      console.log('- Entries count:', result.entries.length);
      console.log('- Status:', result.status);
      console.log('- Settings:', result.settings);
    }
    
  } catch (error) {
    console.error('❌ Error testing MOOE API:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

// Run the test
testMooeAPI();
